<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Button Controls | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Button Controls | DrawnUi Documentation ">
      
      
      <link rel="icon" href="../../images/favicon.ico">
      <link rel="stylesheet" href="../../public/docfx.min.css">
      <link rel="stylesheet" href="../../public/main.css">
      <meta name="docfx:navrel" content="../../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/blob/master/docs/articles/controls/buttons.md/#L1">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../../index.html">
            <img id="logo" class="svg" src="../../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="">
<h1 id="button-controls">Button Controls</h1>

<p>DrawnUi provides highly customizable button controls with platform-specific styling and support for custom content.</p>
<h2 id="skiabutton">SkiaButton</h2>
<p><code>SkiaButton</code> is a versatile button control supporting different button styles, platform-specific appearance, and custom content. You can use the default content or provide your own child views. If you use conventional tags (<code>BtnText</code>, <code>BtnShape</code>), SkiaButton will apply its properties (like <code>Text</code>, <code>TextColor</code>, etc.) to those views automatically.</p>
<blockquote>
<p><strong>Note:</strong> If you provide custom content, use the tags <code>BtnText</code> for your main label and <code>BtnShape</code> for the button background to enable property binding.</p>
</blockquote>
<h3 id="basic-usage">Basic Usage</h3>
<pre><code class="lang-xml">&lt;draw:SkiaButton
    Text=&quot;Click Me&quot;
    WidthRequest=&quot;120&quot;
    HeightRequest=&quot;40&quot;
    BackgroundColor=&quot;Blue&quot;
    TextColor=&quot;White&quot;
    CornerRadius=&quot;8&quot;
    Clicked=&quot;OnButtonClicked&quot; /&gt;
</code></pre>
<h3 id="custom-content-example">Custom Content Example</h3>
<pre><code class="lang-xml">&lt;draw:SkiaButton&gt;
    &lt;draw:SkiaShape Tag=&quot;BtnShape&quot; BackgroundColor=&quot;Red&quot; CornerRadius=&quot;12&quot; /&gt;
    &lt;draw:SkiaLabel Tag=&quot;BtnText&quot; Text=&quot;Custom&quot; TextColor=&quot;Yellow&quot; /&gt;
&lt;/draw:SkiaButton&gt;
</code></pre>
<h3 id="button-style-types">Button Style Types</h3>
<p>SkiaButton supports multiple style variants through the <code>ButtonStyle</code> property:</p>
<ul>
<li><code>Contained</code>: Standard filled button with background color (default)</li>
<li><code>Outlined</code>: Button with outline border and transparent background</li>
<li><code>Text</code>: Button with no background or border, only text</li>
</ul>
<pre><code class="lang-xml">&lt;draw:SkiaButton
    Text=&quot;Outlined Button&quot;
    ButtonStyle=&quot;Outlined&quot;
    BackgroundColor=&quot;Blue&quot;
    TextColor=&quot;Blue&quot; /&gt;
</code></pre>
<h3 id="platform-specific-styling">Platform-Specific Styling</h3>
<p>Platform-specific styles are selected automatically or can be set in code via the <code>UsingControlStyle</code> property (not bindable in XAML). Styles include:</p>
<ul>
<li><code>Cupertino</code>: iOS-style button</li>
<li><code>Material</code>: Android Material Design button</li>
<li><code>Windows</code>: Windows-style button</li>
</ul>
<blockquote>
<p><strong>Note:</strong> There is no <code>ControlStyle</code> bindable property. Platform style is set internally or in code.</p>
</blockquote>
<h3 id="elevation">Elevation</h3>
<p>Buttons can have elevation (shadow) effects:</p>
<pre><code class="lang-xml">&lt;draw:SkiaButton
    Text=&quot;Elevated Button&quot;
    ElevationEnabled=&quot;True&quot; /&gt;
</code></pre>
<h3 id="properties">Properties</h3>
<table>
<thead>
<tr>
<th>Property</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>Text</code></td>
<td>string</td>
<td>The text displayed on the button</td>
</tr>
<tr>
<td><code>TextColor</code></td>
<td>Color</td>
<td>The color of the button text</td>
</tr>
<tr>
<td><code>BackgroundColor</code></td>
<td>Color</td>
<td>The background color of the button</td>
</tr>
<tr>
<td><code>CornerRadius</code></td>
<td>float</td>
<td>The corner radius of the button (applied via <code>BtnShape</code>)</td>
</tr>
<tr>
<td><code>ButtonStyle</code></td>
<td>ButtonStyleType</td>
<td>The button style (Contained, Outlined, Text)</td>
</tr>
<tr>
<td><code>ElevationEnabled</code></td>
<td>bool</td>
<td>Whether the button has a shadow effect</td>
</tr>
<tr>
<td><code>TextCase</code></td>
<td>TextTransform</td>
<td>The text case transformation (None, Uppercase, Lowercase)</td>
</tr>
<tr>
<td><code>FontSize</code></td>
<td>double</td>
<td>The font size of the button text</td>
</tr>
<tr>
<td><code>FontFamily</code></td>
<td>string</td>
<td>The font family of the button text</td>
</tr>
<tr>
<td><code>IsDisabled</code></td>
<td>bool</td>
<td>Disables the button if true</td>
</tr>
<tr>
<td><code>IsPressed</code></td>
<td>bool</td>
<td>True while the button is pressed</td>
</tr>
<tr>
<td><code>IconPosition</code></td>
<td>IconPositionType</td>
<td>Position of icon (icon support planned)</td>
</tr>
<tr>
<td><code>ApplyEffect</code></td>
<td>SkiaTouchAnimation</td>
<td>Touch animation effect (Ripple, Shimmer, etc.)</td>
</tr>
</tbody>
</table>
<h3 id="events">Events</h3>
<ul>
<li><code>Clicked</code>: Raised when the button is clicked/tapped</li>
<li><code>Pressed</code>: Raised when the button is pressed down</li>
<li><code>Released</code>: Raised when the button is released</li>
<li><code>Up</code>, <code>Down</code>, <code>Tapped</code>: Additional gesture events</li>
</ul>
<h3 id="icon-support">Icon Support</h3>
<p>Icon support is planned. The <code>IconPosition</code> property exists, but icon rendering is not yet implemented.</p>
<hr>
<h2 id="api-xml-documentation">API XML Documentation</h2>
<blockquote>
<p>The following methods in SkiaButton have been updated with XML documentation in the codebase:</p>
<ul>
<li><code>OnDown</code>, <code>OnUp</code>, <code>OnTapped</code>, <code>ApplyProperties</code>, <code>CreateDefaultContent</code>, <code>CreateCupertinoStyleContent</code>, <code>CreateMaterialStyleContent</code>, <code>CreateWindowsStyleContent</code>, <code>OnButtonPropertyChanged</code>, <code>FindViews</code>, <code>CreateClip</code>.</li>
</ul>
</blockquote>
<p>For more details, see the source code in <code>src/Engine/Maui/Controls/Button/SkiaButton.cs</code>.</p>

</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/master/docs/articles/controls/buttons.md/#L1" class="edit-link">Edit this page</a>
        </div>

        <div class="next-article d-print-none border-top" id="nextArticle"></div>

      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
