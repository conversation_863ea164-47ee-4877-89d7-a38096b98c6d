<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Advanced Scrolling with SkiaScroll in DrawnUi.Maui | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Advanced Scrolling with SkiaScroll in DrawnUi.Maui | DrawnUi Documentation ">
      
      
      <link rel="icon" href="../../images/favicon.ico">
      <link rel="stylesheet" href="../../public/docfx.min.css">
      <link rel="stylesheet" href="../../public/main.css">
      <meta name="docfx:navrel" content="../../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/blob/master/docs/articles/advanced/skiascroll.md/#L1">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../../index.html">
            <img id="logo" class="svg" src="../../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="">
<h1 id="advanced-scrolling-with-skiascroll-in-drawnuimaui">Advanced Scrolling with SkiaScroll in DrawnUi.Maui</h1>

<p>DrawnUi.Maui’s SkiaScroll control provides high-performance, flexible scrolling for custom UIs, games, dashboards, and data-heavy apps. This article covers advanced usage, virtualization, customization, and best practices for SkiaScroll and related controls.</p>
<h2 id="why-skiascroll">Why SkiaScroll?</h2>
<ul>
<li><strong>Smooth, pixel-perfect scrolling</strong> on all platforms</li>
<li><strong>Supports both vertical, horizontal, and bidirectional scrolling</strong></li>
<li><strong>Virtualization</strong> for large data sets</li>
<li><strong>Customizable headers, footers, and overlays</strong></li>
<li><strong>Pinch-to-zoom and gesture support</strong></li>
<li><strong>Works with any DrawnUi content: layouts, images, shapes, etc.</strong></li>
</ul>
<h2 id="basic-usage">Basic Usage</h2>
<pre><code class="lang-xml">&lt;draw:SkiaScroll Orientation=&quot;Vertical&quot; WidthRequest=&quot;400&quot; HeightRequest=&quot;600&quot;&gt;
    &lt;draw:SkiaLayout Type=&quot;Column&quot; Spacing=&quot;10&quot;&gt;
        &lt;draw:SkiaLabel Text=&quot;Item 1&quot; /&gt;
        &lt;draw:SkiaLabel Text=&quot;Item 2&quot; /&gt;
        &lt;!-- More items --&gt;
    &lt;/draw:SkiaLayout&gt;
&lt;/draw:SkiaScroll&gt;
</code></pre>
<h2 id="multi-directional-and-zoomable-scrolling">Multi-Directional and Zoomable Scrolling</h2>
<pre><code class="lang-xml">&lt;draw:SkiaScroll Orientation=&quot;Both&quot; ZoomLocked=&quot;False&quot; ZoomMin=&quot;1&quot; ZoomMax=&quot;3&quot;&gt;
    &lt;draw:SkiaLayout&gt;
        &lt;draw:SkiaImage Source=&quot;large_map.jpg&quot; /&gt;
    &lt;/draw:SkiaLayout&gt;
&lt;/draw:SkiaScroll&gt;
</code></pre>
<h2 id="virtualization-for-large-data-sets">Virtualization for Large Data Sets</h2>
<p>Enable virtualization for smooth performance with thousands of items:</p>
<pre><code class="lang-xml">&lt;draw:SkiaScroll Virtualisation=&quot;Enabled&quot; Orientation=&quot;Vertical&quot;&gt;
    &lt;draw:SkiaLayout
        Type=&quot;Column&quot;
        ItemsSource=&quot;{Binding LargeItemCollection}&quot;
        Virtualisation=&quot;Enabled&quot;&gt;
        &lt;draw:SkiaLayout.ItemTemplate&gt;
            &lt;DataTemplate&gt;
                &lt;draw:SkiaLabel Text=&quot;{Binding Title}&quot; /&gt;
            &lt;/DataTemplate&gt;
        &lt;/draw:SkiaLayout.ItemTemplate&gt;
    &lt;/draw:SkiaLayout&gt;
&lt;/draw:SkiaScroll&gt;
</code></pre>
<ul>
<li><code>Virtualisation</code> on SkiaScroll controls viewport-based rendering.</li>
<li><code>Virtualisation</code> on SkiaLayout controls the strategy (Enabled, Disabled).</li>
<li>Combine with <code>RecyclingTemplate</code> for template reuse.</li>
<li>Use <code>VirtualisationInflated</code> to control how much content outside the viewport is still rendered.</li>
</ul>
<h2 id="custom-headers-footers-and-overlays">Custom Headers, Footers, and Overlays</h2>
<pre><code class="lang-xml">&lt;draw:SkiaScroll HeaderSticky=&quot;True&quot; HeaderBehind=&quot;False&quot;&gt;
    &lt;draw:SkiaScroll.Header&gt;
        &lt;draw:SkiaShape Type=&quot;Rectangle&quot; BackgroundColor=&quot;#3498DB&quot; HeightRequest=&quot;80&quot;&gt;
            &lt;draw:SkiaLabel Text=&quot;Sticky Header&quot; TextColor=&quot;White&quot; FontSize=&quot;18&quot; /&gt;
        &lt;/draw:SkiaShape&gt;
    &lt;/draw:SkiaScroll.Header&gt;
    &lt;draw:SkiaLayout Type=&quot;Column&quot;&gt;
        &lt;!-- Content --&gt;
    &lt;/draw:SkiaLayout&gt;
    &lt;draw:SkiaScroll.Footer&gt;
        &lt;draw:SkiaShape Type=&quot;Rectangle&quot; BackgroundColor=&quot;#2C3E50&quot; HeightRequest=&quot;60&quot;&gt;
            &lt;draw:SkiaLabel Text=&quot;Footer&quot; TextColor=&quot;White&quot; /&gt;
        &lt;/draw:SkiaShape&gt;
    &lt;/draw:SkiaScroll.Footer&gt;
&lt;/draw:SkiaScroll&gt;
</code></pre>
<h2 id="pull-to-refresh">Pull-to-Refresh</h2>
<pre><code class="lang-xml">&lt;draw:SkiaScroll x:Name=&quot;MyScrollView&quot; Refreshing=&quot;OnRefreshing&quot;&gt;
    &lt;draw:SkiaScroll.RefreshIndicator&gt;
        &lt;draw:RefreshIndicator /&gt;
    &lt;/draw:SkiaScroll.RefreshIndicator&gt;
    &lt;draw:SkiaLayout Type=&quot;Column&quot;&gt;
        &lt;!-- Content items --&gt;
    &lt;/draw:SkiaLayout&gt;
&lt;/draw:SkiaScroll&gt;
</code></pre>
<p>In code-behind:</p>
<pre><code class="lang-csharp">private async void OnRefreshing(object sender, EventArgs e)
{
    // Perform refresh operation
    await LoadDataAsync();
    ((SkiaScroll)sender).EndRefresh();
}
</code></pre>
<h2 id="infinite-and-looped-scrolling">Infinite and Looped Scrolling</h2>
<p>Use SkiaScrollLooped for banners, carousels, or infinite galleries:</p>
<pre><code class="lang-xml">&lt;draw:SkiaScrollLooped Orientation=&quot;Horizontal&quot; IsBanner=&quot;True&quot; CycleSpace=&quot;100&quot;&gt;
    &lt;draw:SkiaLayout Type=&quot;Row&quot;&gt;
        &lt;draw:SkiaImage Source=&quot;image1.jpg&quot; /&gt;
        &lt;draw:SkiaImage Source=&quot;image2.jpg&quot; /&gt;
        &lt;!-- More images --&gt;
    &lt;/draw:SkiaLayout&gt;
&lt;/draw:SkiaScrollLooped&gt;
</code></pre>
<h2 id="programmatic-scrolling-and-position-tracking">Programmatic Scrolling and Position Tracking</h2>
<pre><code class="lang-csharp">// Scroll to a specific position
myScroll.ScrollToPosition(0, 500, true); // Animated scroll to Y=500

// Scroll to a child element
myScroll.ScrollToView(targetElement, true);

// Track scroll position
float y = myScroll.ViewportOffsetY;
</code></pre>
<h2 id="performance-tips">Performance Tips</h2>
<ul>
<li>Enable virtualization for large lists</li>
<li>Use <code>Cache=&quot;Operations&quot;</code> for static or rarely-changing content</li>
<li>Avoid nesting too many scrolls; prefer flat layouts</li>
<li>Use SkiaLabelFps to monitor performance</li>
<li>For custom drawing, override OnDraw in your content controls</li>
</ul>
<h2 id="advanced-custom-scroll-effects-and-gestures">Advanced: Custom Scroll Effects and Gestures</h2>
<ul>
<li>Implement parallax, sticky headers, or custom scroll physics by extending SkiaScroll</li>
<li>Use gesture listeners for advanced input (drag, swipe, pinch)</li>
<li>Combine with SkiaDrawer for overlay panels</li>
</ul>
<h2 id="summary">Summary</h2>
<p>SkiaScroll and related controls provide a robust, high-performance foundation for any scrolling UI in DrawnUi.Maui. With support for virtualization, zoom, custom overlays, and advanced gestures, you can build everything from chat apps to dashboards and games with smooth, responsive scrolling.</p>

</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/master/docs/articles/advanced/skiascroll.md/#L1" class="edit-link">Edit this page</a>
        </div>

        <div class="next-article d-print-none border-top" id="nextArticle"></div>

      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
