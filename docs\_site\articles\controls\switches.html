<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Switches and Toggles | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Switches and Toggles | DrawnUi Documentation ">
      
      
      <link rel="icon" href="../../images/favicon.ico">
      <link rel="stylesheet" href="../../public/docfx.min.css">
      <link rel="stylesheet" href="../../public/main.css">
      <meta name="docfx:navrel" content="../../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/blob/master/docs/articles/controls/switches.md/#L1">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../../index.html">
            <img id="logo" class="svg" src="../../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="">
<h1 id="switches-and-toggles">Switches and Toggles</h1>

<p>DrawnUi provides toggle controls with platform-specific styling, including switches and checkboxes.</p>
<h2 id="skiaswitch">SkiaSwitch</h2>
<p><code>SkiaSwitch</code> is a toggle control styled according to platform conventions, similar to an on/off switch.</p>
<h3 id="basic-usage">Basic Usage</h3>
<pre><code class="lang-xml">&lt;draw:SkiaSwitch
    IsToggled=&quot;false&quot;
    WidthRequest=&quot;50&quot;
    HeightRequest=&quot;30&quot;
    ColorFrameOff=&quot;Gray&quot;
    ColorFrameOn=&quot;Green&quot;
    ColorThumbOff=&quot;White&quot;
    ColorThumbOn=&quot;White&quot;
    Toggled=&quot;OnSwitchToggled&quot; /&gt;
</code></pre>
<h3 id="platform-specific-styling">Platform-Specific Styling</h3>
<p>Set the <code>ControlStyle</code> property to apply platform-specific styling:</p>
<ul>
<li><code>Platform</code>: Automatically selects the appropriate style for the current platform</li>
<li><code>Cupertino</code>: iOS-style switch with pill-shaped track</li>
<li><code>Material</code>: Android Material Design switch</li>
<li><code>Windows</code>: Windows-style switch</li>
</ul>
<pre><code class="lang-xml">&lt;draw:SkiaSwitch
    ControlStyle=&quot;Cupertino&quot;
    IsToggled=&quot;true&quot; /&gt;
</code></pre>
<h3 id="code-behind-example">Code-Behind Example</h3>
<pre><code class="lang-csharp">private void OnSwitchToggled(object sender, bool isToggled)
{
    // Handle the toggle state change
    if (isToggled)
    {
        // Switch is ON
        DisplayAlert(&quot;Switch&quot;, &quot;Turned ON&quot;, &quot;OK&quot;);
    }
    else
    {
        // Switch is OFF
        DisplayAlert(&quot;Switch&quot;, &quot;Turned OFF&quot;, &quot;OK&quot;);
    }
}
</code></pre>
<h3 id="properties">Properties</h3>
<table>
<thead>
<tr>
<th>Property</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>IsToggled</code></td>
<td>bool</td>
<td>Whether the switch is toggled on or off</td>
</tr>
<tr>
<td><code>ColorFrameOn</code></td>
<td>Color</td>
<td>The color of the track when toggled on</td>
</tr>
<tr>
<td><code>ColorFrameOff</code></td>
<td>Color</td>
<td>The color of the track when toggled off</td>
</tr>
<tr>
<td><code>ColorThumbOn</code></td>
<td>Color</td>
<td>The color of the thumb when toggled on</td>
</tr>
<tr>
<td><code>ColorThumbOff</code></td>
<td>Color</td>
<td>The color of the thumb when toggled off</td>
</tr>
<tr>
<td><code>ControlStyle</code></td>
<td>PrebuiltControlStyle</td>
<td>The platform-specific style</td>
</tr>
<tr>
<td><code>IsAnimated</code></td>
<td>bool</td>
<td>Whether state changes are animated</td>
</tr>
<tr>
<td><code>AnimationSpeed</code></td>
<td>uint</td>
<td>Animation duration in milliseconds (default: 200)</td>
</tr>
</tbody>
</table>
<h3 id="events">Events</h3>
<ul>
<li><code>Toggled</code>: Raised when the switch is toggled on or off
<ul>
<li>Event signature: <code>EventHandler&lt;bool&gt;</code></li>
<li>The bool parameter indicates the new toggle state (true = on, false = off)</li>
</ul>
</li>
</ul>
<h2 id="skiacheckbox">SkiaCheckbox</h2>
<p><code>SkiaCheckbox</code> is a toggle control styled as a checkbox with platform-specific appearance.</p>
<h3 id="basic-usage-1">Basic Usage</h3>
<pre><code class="lang-xml">&lt;draw:SkiaCheckbox
    IsToggled=&quot;false&quot;
    WidthRequest=&quot;24&quot;
    HeightRequest=&quot;24&quot;
    ColorFrameOff=&quot;Gray&quot;
    ColorFrameOn=&quot;Blue&quot;
    ColorThumbOff=&quot;Transparent&quot;
    ColorThumbOn=&quot;White&quot;
    Toggled=&quot;OnCheckboxToggled&quot; /&gt;
</code></pre>
<h3 id="platform-specific-styling-1">Platform-Specific Styling</h3>
<p>Like SkiaSwitch, SkiaCheckbox supports platform-specific styling through the <code>ControlStyle</code> property.</p>
<h3 id="properties-1">Properties</h3>
<p>SkiaCheckbox shares most properties with SkiaSwitch, both inheriting from SkiaToggle.</p>
<h2 id="skiatoggle">SkiaToggle</h2>
<p><code>SkiaToggle</code> is the base class for toggle controls. You can use it to create custom toggle controls with similar behavior to switches and checkboxes.</p>
<h3 id="key-properties">Key Properties</h3>
<table>
<thead>
<tr>
<th>Property</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>IsToggled</code></td>
<td>bool</td>
<td>Whether the control is toggled on or off</td>
</tr>
<tr>
<td><code>DefaultValue</code></td>
<td>bool</td>
<td>The default toggle state</td>
</tr>
<tr>
<td><code>ColorFrameOn/Off</code></td>
<td>Color</td>
<td>The color of the frame in each state</td>
</tr>
<tr>
<td><code>ColorThumbOn/Off</code></td>
<td>Color</td>
<td>The color of the thumb in each state</td>
</tr>
<tr>
<td><code>IsAnimated</code></td>
<td>bool</td>
<td>Whether state changes are animated</td>
</tr>
</tbody>
</table>
<h3 id="events-1">Events</h3>
<ul>
<li><code>Toggled</code>: Raised when the toggle state changes
<ul>
<li>Event signature: <code>EventHandler&lt;bool&gt;</code></li>
<li>The bool parameter indicates the new toggle state</li>
</ul>
</li>
</ul>
<h2 id="skiaradiobutton">SkiaRadioButton</h2>
<p><code>SkiaRadioButton</code> is a specialized toggle control for selecting one option from a group of mutually exclusive options. It's subclassed from SkiaToggle and provides radio button functionality.</p>
<h3 id="basic-usage-2">Basic Usage</h3>
<pre><code class="lang-xml">&lt;draw:SkiaLayout Type=&quot;Column&quot; Spacing=&quot;10&quot;&gt;
    &lt;draw:SkiaRadioButton
        GroupName=&quot;Options&quot;
        Text=&quot;Option 1&quot;
        IsToggled=&quot;true&quot;
        WidthRequest=&quot;150&quot;
        HeightRequest=&quot;30&quot; /&gt;
    &lt;draw:SkiaRadioButton
        GroupName=&quot;Options&quot;
        Text=&quot;Option 2&quot;
        IsToggled=&quot;false&quot;
        WidthRequest=&quot;150&quot;
        HeightRequest=&quot;30&quot; /&gt;
    &lt;draw:SkiaRadioButton
        GroupName=&quot;Options&quot;
        Text=&quot;Option 3&quot;
        IsToggled=&quot;false&quot;
        WidthRequest=&quot;150&quot;
        HeightRequest=&quot;30&quot; /&gt;
&lt;/draw:SkiaLayout&gt;
</code></pre>
<h3 id="key-properties-1">Key Properties</h3>
<table>
<thead>
<tr>
<th>Property</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>GroupName</code></td>
<td>string</td>
<td>Name of the radio button group for mutual exclusion</td>
</tr>
<tr>
<td><code>Text</code></td>
<td>string</td>
<td>Text label for the radio button</td>
</tr>
<tr>
<td><code>IsToggled</code></td>
<td>bool</td>
<td>Whether this radio button is selected</td>
</tr>
</tbody>
</table>
<h3 id="behavior">Behavior</h3>
<ul>
<li>Only one radio button in a group (same <code>GroupName</code>) can be selected at a time</li>
<li>Selecting one radio button automatically deselects others in the same group</li>
<li>Inherits all properties and events from SkiaToggle</li>
</ul>

</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/master/docs/articles/controls/switches.md/#L1" class="edit-link">Edit this page</a>
        </div>

        <div class="next-article d-print-none border-top" id="nextArticle"></div>

      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
