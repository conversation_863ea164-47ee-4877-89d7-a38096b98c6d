<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Native Integration | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Native Integration | DrawnUi Documentation ">
      
      
      <link rel="icon" href="../../images/favicon.ico">
      <link rel="stylesheet" href="../../public/docfx.min.css">
      <link rel="stylesheet" href="../../public/main.css">
      <meta name="docfx:navrel" content="../../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/blob/master/docs/articles/controls/native-integration.md/#L1">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../../index.html">
            <img id="logo" class="svg" src="../../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="">
<h1 id="native-integration">Native Integration</h1>

<p>DrawnUi.Maui provides seamless integration with native MAUI controls through the <code>SkiaMauiElement</code> control. This allows you to embed standard MAUI controls like WebView, MediaElement, and others within your DrawnUI canvas while maintaining hardware acceleration and performance.</p>
<h2 id="skiamauielement">SkiaMauiElement</h2>
<p><code>SkiaMauiElement</code> is a wrapper control that enables embedding native MAUI <code>VisualElement</code> controls within the DrawnUI rendering pipeline. This is essential for controls that require native platform implementations or when you need to integrate existing MAUI controls into your DrawnUI application.</p>
<h3 id="key-features">Key Features</h3>
<ul>
<li><strong>Native Control Embedding</strong>: Wrap any MAUI VisualElement within DrawnUI</li>
<li><strong>Platform Optimization</strong>: Automatic platform-specific handling (snapshots on Windows, direct rendering on other platforms)</li>
<li><strong>Gesture Coordination</strong>: Proper gesture handling between DrawnUI and native controls</li>
<li><strong>Binding Support</strong>: Full data binding support for embedded controls</li>
<li><strong>Performance</strong>: Optimized rendering with minimal overhead</li>
</ul>
<h3 id="basic-usage">Basic Usage</h3>
<pre><code class="lang-xml">&lt;draw:SkiaMauiElement
    HorizontalOptions=&quot;Fill&quot;
    VerticalOptions=&quot;Fill&quot;&gt;
    
    &lt;!-- Any MAUI VisualElement can be embedded --&gt;
    &lt;Entry
        Placeholder=&quot;Enter text here&quot;
        Text=&quot;{Binding UserInput}&quot; /&gt;
        
&lt;/draw:SkiaMauiElement&gt;
</code></pre>
<h3 id="webview-integration">WebView Integration</h3>
<p>One of the most common use cases is embedding a WebView for displaying web content within your DrawnUI application:</p>
<pre><code class="lang-xml">&lt;draw:SkiaLayout
    HorizontalOptions=&quot;Fill&quot;
    VerticalOptions=&quot;Fill&quot;
    Type=&quot;Column&quot;&gt;

    &lt;!-- Header --&gt;
    &lt;draw:SkiaLayout
        BackgroundColor=&quot;{StaticResource Gray600}&quot;
        HorizontalOptions=&quot;Fill&quot;
        HeightRequest=&quot;60&quot;
        Type=&quot;Row&quot;
        Spacing=&quot;16&quot;
        Padding=&quot;16,0&quot;&gt;

        &lt;draw:SkiaButton
            Text=&quot;← Back&quot;
            TextColor=&quot;White&quot;
            BackgroundColor=&quot;Transparent&quot;
            VerticalOptions=&quot;Center&quot; /&gt;

        &lt;draw:SkiaLabel
            x:Name=&quot;LabelTitle&quot;
            Text=&quot;Web Browser&quot;
            TextColor=&quot;White&quot;
            FontSize=&quot;18&quot;
            VerticalOptions=&quot;Center&quot;
            HorizontalOptions=&quot;Start&quot; /&gt;

    &lt;/draw:SkiaLayout&gt;

    &lt;!-- Background --&gt;
    &lt;draw:SkiaControl
        BackgroundColor=&quot;{StaticResource Gray600}&quot;
        HorizontalOptions=&quot;Fill&quot;
        VerticalOptions=&quot;Fill&quot;
        ZIndex=&quot;-1&quot; /&gt;

    &lt;!-- WebView Content --&gt;
    &lt;draw:SkiaMauiElement
        Margin=&quot;1,0&quot;
        HorizontalOptions=&quot;Fill&quot;
        VerticalOptions=&quot;Fill&quot;&gt;

        &lt;WebView
            x:Name=&quot;ControlBrowser&quot;
            HorizontalOptions=&quot;FillAndExpand&quot;
            VerticalOptions=&quot;FillAndExpand&quot; /&gt;

    &lt;/draw:SkiaMauiElement&gt;

&lt;/draw:SkiaLayout&gt;
</code></pre>
<h3 id="code-behind-implementation">Code-Behind Implementation</h3>
<pre><code class="lang-csharp">public partial class ScreenBrowser
{
    public ScreenBrowser(string title, string source, bool isUrl = true)
    {
        InitializeComponent();

        LabelTitle.Text = title;

        if (isUrl)
        {
            if (string.IsNullOrEmpty(source))
            {
                source = &quot;about:blank&quot;;
            }
            var url = new UrlWebViewSource
            {
                Url = source
            };
            ControlBrowser.Source = url;
        }
        else
        {
            if (string.IsNullOrEmpty(source))
            {
                source = &quot;&quot;;
            }
            var html = new HtmlWebViewSource
            {
                Html = source
            };
            ControlBrowser.Source = html;
        }
    }
}
</code></pre>
<h3 id="platform-specific-behavior">Platform-Specific Behavior</h3>
<p><code>SkiaMauiElement</code> handles platform differences automatically:</p>
<p><strong>Windows:</strong></p>
<ul>
<li>Uses bitmap snapshots for rendering native controls within the SkiaSharp canvas</li>
<li>Automatic snapshot updates when control content changes</li>
<li>Optimized for performance with caching</li>
</ul>
<p><strong>iOS/Android:</strong></p>
<ul>
<li>Direct native view positioning and transformation</li>
<li>No snapshot overhead - native controls are moved/transformed directly</li>
<li>Better performance and native feel</li>
</ul>
<h3 id="common-integration-scenarios">Common Integration Scenarios</h3>
<h4 id="media-playback">Media Playback</h4>
<pre><code class="lang-xml">&lt;draw:SkiaMauiElement
    HorizontalOptions=&quot;Fill&quot;
    HeightRequest=&quot;200&quot;&gt;
    
    &lt;MediaElement
        Source=&quot;video.mp4&quot;
        ShowsPlaybackControls=&quot;True&quot;
        AutoPlay=&quot;False&quot; /&gt;
        
&lt;/draw:SkiaMauiElement&gt;
</code></pre>
<h4 id="datetime-pickers">Date/Time Pickers</h4>
<pre><code class="lang-xml">&lt;draw:SkiaLayout Type=&quot;Column&quot; Spacing=&quot;16&quot;&gt;
    
    &lt;draw:SkiaMauiElement HeightRequest=&quot;50&quot;&gt;
        &lt;DatePicker
            Date=&quot;{Binding SelectedDate}&quot;
            Format=&quot;dd/MM/yyyy&quot; /&gt;
    &lt;/draw:SkiaMauiElement&gt;
    
    &lt;draw:SkiaMauiElement HeightRequest=&quot;50&quot;&gt;
        &lt;TimePicker
            Time=&quot;{Binding SelectedTime}&quot;
            Format=&quot;HH:mm&quot; /&gt;
    &lt;/draw:SkiaMauiElement&gt;
    
&lt;/draw:SkiaLayout&gt;
</code></pre>
<h4 id="native-picker">Native Picker</h4>
<pre><code class="lang-xml">&lt;draw:SkiaMauiElement HeightRequest=&quot;50&quot;&gt;
    &lt;Picker
        Title=&quot;Select an option&quot;
        ItemsSource=&quot;{Binding Options}&quot;
        SelectedItem=&quot;{Binding SelectedOption}&quot; /&gt;
&lt;/draw:SkiaMauiElement&gt;
</code></pre>
<h3 id="properties">Properties</h3>
<table>
<thead>
<tr>
<th>Property</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>Content</code></td>
<td>VisualElement</td>
<td>The native MAUI control to embed</td>
</tr>
</tbody>
</table>
<h3 id="important-notes">Important Notes</h3>
<ul>
<li><strong>Content Property</strong>: Use the <code>Content</code> property to set the embedded control, not child elements</li>
<li><strong>Sizing</strong>: The SkiaMauiElement will size itself based on the embedded control's requirements</li>
<li><strong>Gestures</strong>: Native controls handle their own gestures; DrawnUI gestures work outside the embedded area</li>
<li><strong>Performance</strong>: Consider the platform-specific rendering approach when designing your layout</li>
<li><strong>Binding Context</strong>: The embedded control automatically inherits the binding context</li>
</ul>
<h3 id="limitations">Limitations</h3>
<ul>
<li>Cannot have SkiaControl subviews (use Content property instead)</li>
<li>Platform-specific rendering differences may affect visual consistency</li>
<li>Some complex native controls may have gesture conflicts</li>
</ul>
<h3 id="best-practices">Best Practices</h3>
<ol>
<li><strong>Use Sparingly</strong>: Only embed native controls when necessary (e.g., WebView, MediaElement)</li>
<li><strong>Size Appropriately</strong>: Set explicit sizes when possible to avoid layout issues</li>
<li><strong>Test on All Platforms</strong>: Verify behavior across iOS, Android, and Windows</li>
<li><strong>Consider Alternatives</strong>: Check if DrawnUI has a native equivalent before embedding</li>
<li><strong>Performance</strong>: Monitor performance impact, especially with multiple embedded controls</li>
</ol>

</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/master/docs/articles/controls/native-integration.md/#L1" class="edit-link">Edit this page</a>
        </div>

        <div class="next-article d-print-none border-top" id="nextArticle"></div>

      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
