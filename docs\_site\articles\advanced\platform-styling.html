<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Platform-Specific Styling | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Platform-Specific Styling | DrawnUi Documentation ">
      
      
      <link rel="icon" href="../../images/favicon.ico">
      <link rel="stylesheet" href="../../public/docfx.min.css">
      <link rel="stylesheet" href="../../public/main.css">
      <meta name="docfx:navrel" content="../../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/blob/master/docs/articles/advanced/platform-styling.md/#L1">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../../index.html">
            <img id="logo" class="svg" src="../../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="">
<h1 id="platform-specific-styling">Platform-Specific Styling</h1>

<p>DrawnUi controls support platform-specific styling to ensure your app looks and feels native on each platform.</p>
<h2 id="using-platform-styles">Using Platform Styles</h2>
<h3 id="the-controlstyle-property">The ControlStyle Property</h3>
<p>Many DrawnUi controls include a <code>ControlStyle</code> property that determines their visual appearance:</p>
<ul>
<li><code>Unset</code>: Default styling defined by the control</li>
<li><code>Platform</code>: Automatically selects the appropriate style for the current platform</li>
<li><code>Cupertino</code>: iOS-style appearance</li>
<li><code>Material</code>: Android Material Design appearance</li>
<li><code>Windows</code>: Windows-style appearance</li>
</ul>
<h3 id="basic-usage">Basic Usage</h3>
<pre><code class="lang-xml">&lt;!-- Automatically use the platform-specific style --&gt;
&lt;draw:SkiaButton
    Text=&quot;Platform Button&quot;
    ControlStyle=&quot;Platform&quot; /&gt;

&lt;!-- Explicitly use iOS style on any platform --&gt;
&lt;draw:SkiaSwitch
    ControlStyle=&quot;Cupertino&quot;
    IsToggled=&quot;true&quot; /&gt;
</code></pre>
<h2 id="supported-controls">Supported Controls</h2>
<p>The following controls support platform-specific styling:</p>
<ul>
<li><code>SkiaButton</code>: Different button appearances across platforms</li>
<li><code>SkiaSwitch</code>: Toggle switches with platform-specific track and thumb styling</li>
<li><code>SkiaCheckbox</code>: Checkbox controls with platform-appropriate checkmarks and animations</li>
</ul>
<h2 id="platform-style-characteristics">Platform Style Characteristics</h2>
<h3 id="cupertino-ios-style">Cupertino (iOS) Style</h3>
<ul>
<li>Rounded corners and subtle shadows</li>
<li>Blue accent color (#007AFF)</li>
<li>Switches have pill-shaped tracks with shadows on the thumb</li>
<li>Buttons typically have semibold text</li>
</ul>
<h3 id="material-android-style">Material (Android) Style</h3>
<ul>
<li>Less rounded corners</li>
<li>More pronounced shadows</li>
<li>Material blue accent color (#2196F3)</li>
<li>Switches have track colors that match the thumb when active</li>
<li>Buttons often use uppercase text</li>
</ul>
<h3 id="windows-style">Windows Style</h3>
<ul>
<li>Minimal corner radius</li>
<li>Subtle shadows</li>
<li>Windows blue accent color (#0078D7)</li>
<li>Switches and buttons have a more squared appearance</li>
</ul>
<h2 id="customizing-platform-styles">Customizing Platform Styles</h2>
<p>You can combine platform styles with custom styling. The platform style defines the base appearance, while your custom properties provide additional customization:</p>
<pre><code class="lang-xml">&lt;draw:SkiaButton
    Text=&quot;Custom Platform Button&quot;
    ControlStyle=&quot;Platform&quot;
    BackgroundColor=&quot;Purple&quot;
    TextColor=&quot;White&quot; /&gt;
</code></pre>
<p>This creates a button with the platform-specific shape, shadow, and behavior, but with your custom colors.</p>
<h2 id="creating-custom-platform-styled-controls">Creating Custom Platform-Styled Controls</h2>
<p>If you're creating custom controls, you can leverage the same platform styling system:</p>
<pre><code class="lang-csharp">public class MyCustomControl : SkiaControl
{
    public static readonly BindableProperty ControlStyleProperty = BindableProperty.Create(
        nameof(ControlStyle),
        typeof(PrebuiltControlStyle),
        typeof(MyCustomControl),
        PrebuiltControlStyle.Unset);

    public PrebuiltControlStyle ControlStyle
    {
        get { return (PrebuiltControlStyle)GetValue(ControlStyleProperty); }
        set { SetValue(ControlStyleProperty, value); }
    }
    
    protected override void OnPropertyChanged(string propertyName = null)
    {
        base.OnPropertyChanged(propertyName);
        
        if (propertyName == nameof(ControlStyle))
        {
            ApplyPlatformStyle();
        }
    }
    
    private void ApplyPlatformStyle()
    {
        switch (ControlStyle)
        {
            case PrebuiltControlStyle.Cupertino:
                // Apply iOS-specific styling
                break;
            case PrebuiltControlStyle.Material:
                // Apply Material Design styling
                break;
            case PrebuiltControlStyle.Windows:
                // Apply Windows styling
                break;
            case PrebuiltControlStyle.Platform:
                #if IOS || MACCATALYST
                // Apply iOS styling
                #elif ANDROID
                // Apply Material styling
                #elif WINDOWS
                // Apply Windows styling
                #endif
                break;
        }
    }
}
</code></pre>

</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/master/docs/articles/advanced/platform-styling.md/#L1" class="edit-link">Edit this page</a>
        </div>

        <div class="next-article d-print-none border-top" id="nextArticle"></div>

      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
