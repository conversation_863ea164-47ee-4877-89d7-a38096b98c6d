<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Layout System Architecture | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Layout System Architecture | DrawnUi Documentation ">
      
      
      <link rel="icon" href="../../images/favicon.ico">
      <link rel="stylesheet" href="../../public/docfx.min.css">
      <link rel="stylesheet" href="../../public/main.css">
      <meta name="docfx:navrel" content="../../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/blob/master/docs/articles/advanced/layout-system.md/#L1">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../../index.html">
            <img id="logo" class="svg" src="../../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="">
<h1 id="layout-system-architecture">Layout System Architecture</h1>

<p>This article covers the internal architecture of DrawnUi.Maui's layout system, designed for developers who want to understand how layouts work under the hood or extend the system with custom layout types.</p>
<h2 id="layout-system-overview">Layout System Overview</h2>
<p>DrawnUi.Maui's layout system is built on a core principle: direct rendering to canvas with optimizations for mobile and desktop platforms. Unlike traditional MAUI layouts that create native UI elements, DrawnUi.Maui renders everything using SkiaSharp, enabling consistent cross-platform visuals and better performance for complex UIs.</p>
<h2 id="core-components">Core Components</h2>
<h3 id="skiacontrol">SkiaControl</h3>
<p><code>SkiaControl</code> is the foundation of the entire UI system. It provides core capabilities for:</p>
<ul>
<li>Position tracking in the rendering tree</li>
<li>Coordinate transformation for touch and rendering</li>
<li>Efficient invalidation system</li>
<li>Support for effects and transforms</li>
<li>Hit testing and touch input handling</li>
<li>Visibility management</li>
</ul>
<p>Its key methods include:</p>
<ul>
<li><code>OnMeasure</code>: Determines the size requirements of the control</li>
<li><code>OnArrange</code>: Positions the control within its parent</li>
<li><code>OnDraw</code>: Renders the control using a SkiaSharp canvas</li>
<li><code>InvalidateInternal</code>: Manages rendering invalidation</li>
</ul>
<h3 id="skialayout">SkiaLayout</h3>
<p><code>SkiaLayout</code> extends <code>SkiaControl</code> to provide layout functionality. It's implemented as a partial class with functionality split across files by layout type:</p>
<ul>
<li><strong>SkiaLayout.cs</strong>: Core layout mechanisms</li>
<li><strong>SkiaLayout.Grid.cs</strong>: Grid layout implementation</li>
<li><strong>SkiaLayout.ColumnRow.cs</strong>: Stack-like layouts</li>
<li><strong>SkiaLayout.BuildWrapLayout.cs</strong>: Wrap layout implementation</li>
<li><strong>SkiaLayout.ListView.cs</strong>: Virtualized list rendering</li>
<li><strong>SkiaLayout.IList.cs</strong>: List-specific optimization</li>
<li><strong>SkiaLayout.ViewsAdapter.cs</strong>: Template management</li>
</ul>
<p>This approach allows specialized handling for each layout type while sharing common infrastructure.</p>
<h3 id="layout-structures">Layout Structures</h3>
<p>The system uses specialized structures to efficiently track and manage layout calculations:</p>
<ul>
<li><strong>LayoutStructure</strong>: Tracks arranged controls in stack layouts</li>
<li><strong>GridStructure</strong>: Manages grid-specific layout information</li>
<li><strong>ControlInStack</strong>: Contains information about a control's position</li>
</ul>
<h2 id="advanced-concepts">Advanced Concepts</h2>
<h3 id="virtualization">Virtualization</h3>
<p>Virtualization is a key performance optimization that only renders items currently visible in the viewport. This enables efficient rendering of large collections.</p>
<p>The <code>VirtualizationMode</code> enum defines several strategies:</p>
<ul>
<li><strong>None</strong>: All items are rendered</li>
<li><strong>Enabled</strong>: Only visible items are rendered and measured</li>
<li><strong>Smart</strong>: Renders visible items plus a buffer</li>
<li><strong>Managed</strong>: Uses a managed renderer for advanced cases</li>
</ul>
<p>Virtualization works alongside template recycling to minimize both CPU and memory usage.</p>
<h3 id="template-recycling">Template Recycling</h3>
<p>The <code>RecyclingTemplate</code> property determines how templates are reused across items:</p>
<ul>
<li><strong>None</strong>: New instance created for each item</li>
<li><strong>Enabled</strong>: Templates are reused as items scroll out of view</li>
<li><strong>Smart</strong>: Reuses templates with additional optimizations</li>
</ul>
<p>The <code>ViewsAdapter</code> class manages template instantiation, recycling, and state management.</p>
<h3 id="measurement-strategies">Measurement Strategies</h3>
<p>The layout system supports different strategies for measuring item sizes:</p>
<ul>
<li><strong>MeasureFirst</strong>: Measures all items before rendering</li>
<li><strong>MeasureAll</strong>: Continuously measures all items</li>
<li><strong>MeasureVisible</strong>: Only measures visible items</li>
</ul>
<p>These strategies let you balance between layout accuracy and performance.</p>
<h2 id="extending-the-layout-system">Extending the Layout System</h2>
<h3 id="creating-a-custom-layout-type">Creating a Custom Layout Type</h3>
<p>To create a custom layout type, you'll typically:</p>
<ol>
<li>Create a new class inheriting from <code>SkiaLayout</code></li>
<li>Override the <code>OnMeasure</code> and <code>OnArrange</code> methods</li>
<li>Implement custom measurement and arrangement logic</li>
<li>Optionally create custom properties for layout configuration</li>
</ol>
<p>Here's a simplified example of a circular layout implementation:</p>
<pre><code class="lang-csharp">public class CircularLayout : SkiaLayout
{
    public static readonly BindableProperty RadiusProperty = 
        BindableProperty.Create(nameof(Radius), typeof(float), typeof(CircularLayout), 100f,
        propertyChanged: (b, o, n) =&gt; ((CircularLayout)b).InvalidateMeasure());
        
    public float Radius
    {
        get =&gt; (float)GetValue(RadiusProperty);
        set =&gt; SetValue(RadiusProperty, value);
    }
    
    protected override SKSize OnMeasure(float widthConstraint, float heightConstraint)
    {
        // Need enough space for a circle with our radius
        return new SKSize(Radius * 2, Radius * 2);
    }
    
    protected override void OnArrange(SKRect destination)
    {
        base.OnArrange(destination);
        
        // Skip if no children
        if (Children.Count == 0) return;
        
        // Calculate center point
        SKPoint center = new SKPoint(destination.MidX, destination.MidY);
        float angleStep = 360f / Children.Count;
        
        // Position each child around the circle
        for (int i = 0; i &lt; Children.Count; i++)
        {
            var child = Children[i];
            if (!child.IsVisible) continue;
            
            // Calculate position on circle
            float angle = i * angleStep * (float)Math.PI / 180f;
            float x = center.X + Radius * (float)Math.Cos(angle) - child.MeasuredSize.Width / 2;
            float y = center.Y + Radius * (float)Math.Sin(angle) - child.MeasuredSize.Height / 2;
            
            // Arrange child at calculated position
            child.Arrange(new SKRect(x, y, x + child.MeasuredSize.Width, y + child.MeasuredSize.Height));
        }
    }
}
</code></pre>
<h3 id="best-practices-for-custom-layouts">Best Practices for Custom Layouts</h3>
<ol>
<li><p><strong>Minimize Measure Calls</strong>: Measure operations are expensive. Cache results when possible.</p>
</li>
<li><p><strong>Implement Proper Invalidation</strong>: Ensure your layout properly invalidates when properties affecting layout change.</p>
</li>
<li><p><strong>Consider Virtualization</strong>: For layouts with many items, implement virtualization to only render visible content.</p>
</li>
<li><p><strong>Optimize Arrangement Logic</strong>: Keep arrangement logic simple and efficient, especially for layouts that update frequently.</p>
</li>
<li><p><strong>Respect Constraints</strong>: Always respect the width and height constraints passed to OnMeasure.</p>
</li>
<li><p><strong>Cache Layout Calculations</strong>: For complex layouts, consider caching calculations that don't need to be redone every frame.</p>
</li>
<li><p><strong>Extend SkiaLayout</strong>: Instead of creating entirely new layout types, consider extending SkiaLayout and creating a new LayoutType enum value if needed.</p>
</li>
</ol>
<h2 id="layout-system-internals">Layout System Internals</h2>
<h3 id="the-layout-process">The Layout Process</h3>
<p>The layout process follows these steps:</p>
<ol>
<li><strong>Parent Invalidates Layout</strong>: When a change requires remeasurement</li>
<li><strong>OnMeasure Called</strong>: Layout determines its size requirements</li>
<li><strong>Parent Determines Size</strong>: Parent decides actual size allocation</li>
<li><strong>OnArrange Called</strong>: Layout positions itself and its children</li>
<li><strong>OnDraw Called</strong>: Layout renders itself and its children</li>
</ol>
<h3 id="coordinate-spaces">Coordinate Spaces</h3>
<p>The layout system deals with multiple coordinate spaces:</p>
<ul>
<li><strong>Local Space</strong>: Relative to the control itself (0,0 is top-left of control)</li>
<li><strong>Parent Space</strong>: Relative to the parent control</li>
<li><strong>Canvas Space</strong>: Relative to the drawing canvas</li>
<li><strong>Screen Space</strong>: Relative to the screen (used for touch input)</li>
</ul>
<p>The system provides methods for converting between these spaces, making it easier to handle positioning and hit testing.</p>
<h3 id="layout-specific-properties">Layout-Specific Properties</h3>
<p>Layout controls have unique bindable properties that affect their behavior:</p>
<ul>
<li><strong>ColumnDefinitions/RowDefinitions</strong>: Define grid structure</li>
<li><strong>Spacing</strong>: Controls space between items</li>
<li><strong>Padding</strong>: Controls space inside the layout edges</li>
<li><strong>LayoutType</strong>: Determines layout strategy</li>
<li><strong>ItemsSource/ItemTemplate</strong>: For data-driven layouts</li>
</ul>
<h2 id="performance-considerations">Performance Considerations</h2>
<h3 id="rendering-optimization">Rendering Optimization</h3>
<p>The rendering system is optimized using several techniques:</p>
<ol>
<li><strong>Clipping</strong>: Only renders content within visible bounds</li>
<li><strong>Caching</strong>: Different caching strategies for balancing performance</li>
<li><strong>Background Processing</strong>: Template initialization on background threads</li>
<li><strong>Incremental Loading</strong>: Loading and measuring items incrementally</li>
</ol>
<h3 id="when-to-use-each-layout-type">When to Use Each Layout Type</h3>
<ul>
<li><strong>Absolute</strong>: When precise positioning is needed (graphs, custom visualizations)</li>
<li><strong>Grid</strong>: For tabular data and form layouts</li>
<li><strong>Column/Row</strong>: For sequential content in one direction</li>
<li><strong>Wrap</strong>: For content that should flow naturally across lines (tags, flow layouts)</li>
</ul>
<h2 id="debugging-layouts">Debugging Layouts</h2>
<p>For debugging layout issues, use these built-in features:</p>
<ul>
<li>Set <code>IsDebugRenderBounds</code> to <code>true</code> to visualize layout boundaries</li>
<li>Use <code>SkiaLabelFps</code> to monitor rendering performance</li>
<li>Add the <code>DebugRenderGraph</code> control to visualize the rendering tree</li>
</ul>
<h2 id="summary">Summary</h2>
<p>DrawnUi.Maui's layout system provides a foundation for creating high-performance, visually consistent UIs across platforms. By understanding its architecture, you can leverage its capabilities to create custom layouts and optimize your application's performance.</p>

</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/master/docs/articles/advanced/layout-system.md/#L1" class="edit-link">Edit this page</a>
        </div>

        <div class="next-article d-print-none border-top" id="nextArticle"></div>

      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
