<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>DrawnUi.Maui | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="DrawnUi.Maui | DrawnUi Documentation ">
      
      
      <link rel="icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/blob/master/docs/articles/index.md/#L1">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="">
<h1 id="drawnuimaui">DrawnUi.Maui</h1>

<p><strong>Build beautiful, high-performance mobile apps with C# code-behind instead of XAML</strong></p>
<p>DrawnUi is a rendering engine that draws your entire UI on a hardware-accelerated Skia canvas. Create pixel-perfect custom controls with gestures and animations, all powered by <a href="https://github.com/mono/SkiaSharp">SkiaSharp</a> 😍.</p>
<p><strong>Supports:</strong> iOS • MacCatalyst • Android • Windows</p>
<hr>
<h2 id="-quick-start">🚀 Quick Start</h2>
<h3 id="1-install-drawnui">1. Install DrawnUi</h3>
<pre><code class="lang-bash">dotnet add package DrawnUi.Maui
</code></pre>
<h3 id="2-learn-the-basics">2. Learn the Basics</h3>
<ul>
<li><strong><a href="getting-started.html">📖 Getting Started</a></strong> - Installation and setup guide</li>
<li><strong><a href="first-app.html">🎯 Your First App</a></strong> - Build your first DrawnUI app in 5 minutes</li>
<li><strong><a href="fluent-extensions.html">⚡ Fluent Extensions</a></strong> - Master the code-behind fluent API</li>
</ul>
<h3 id="3-explore-controls">3. Explore Controls</h3>
<ul>
<li><strong><a href="controls/index.html">🎛️ All Controls</a></strong> - Buttons, layouts, animations, and more</li>
<li><strong><a href="../demo.html">📱 Live Demo</a></strong> - Interactive examples you can try</li>
</ul>
<h3 id="4-go-advanced">4. Go Advanced</h3>
<ul>
<li><strong><a href="advanced/index.html">🏗️ Advanced Topics</a></strong> - Architecture, performance, and platform-specific features</li>
<li><strong><a href="../api/index.html">📚 API Reference</a></strong> - Complete technical documentation</li>
</ul>
<hr>
<h2 id="-why-drawnui">✨ Why DrawnUi?</h2>
<p><strong>🎨 Code-Behind First</strong></p>
<ul>
<li>Write UI in C# with fluent extensions - no XAML needed</li>
<li>Type-safe, IntelliSense-friendly development</li>
<li>Reactive property observation without traditional bindings</li>
</ul>
<p><strong>⚡ High Performance</strong></p>
<ul>
<li>Hardware-accelerated Skia rendering</li>
<li>Efficient caching and virtualization</li>
<li>Smooth 60fps animations and gestures</li>
</ul>
<p><strong>🎯 Pixel Perfect</strong></p>
<ul>
<li>Consistent UI across all platforms</li>
<li>Custom controls that look exactly how you want</li>
<li>Full control over every pixel</li>
</ul>
<p><strong>🔧 Flexible Architecture</strong></p>
<ul>
<li>Use alongside existing MAUI controls</li>
<li>Or go fully drawn with SkiaShell navigation</li>
<li>MIT licensed and production-ready</li>
</ul>
<hr>
<h2 id="-see-it-in-action">📱 See It In Action</h2>
<p><strong>Live Examples:</strong></p>
<ul>
<li><strong><a href="https://github.com/taublast/AppoMobi.Maui.DrawnUi.Demo">Engine Demo</a></strong> - Comprehensive control showcase</li>
<li><strong><a href="https://github.com/taublast/AppoMobi.Maui.DrawnUi.SpaceShooter">Space Shooter Game</a></strong> - Full arcade game built with DrawnUI</li>
<li><strong><a href="https://github.com/taublast/SurfAppCompareDrawn">CollectionView Demo</a></strong> - Performance comparison with native controls</li>
<li><strong><a href="https://github.com/taublast/ShadersCarousel/">Shaders Carousel</a></strong> - Advanced SkiaSharp v3 effects</li>
</ul>
<hr>
<h2 id="-frequently-asked-questions">❓ Frequently Asked Questions</h2>
<h3 id="getting-started">Getting Started</h3>
<p><strong>Q: Can I use DrawnUI with .NET 9?</strong>
A: Yes! DrawnUI works with .NET 9. Remember that SkiaLabel, SkiaLayout etc. are virtual drawn controls that must be placed inside a <code>Canvas</code>: <code>&lt;draw:Canvas&gt;your skia controls&lt;/draw:Canvas&gt;</code>. Only <code>Canvas</code> has handlers for normal and hardware accelerated views.</p>
<p><strong>Q: Can I use MAUI's default Images folder with DrawnUI?</strong>
A: Unfortunately no. DrawnUI can read from <code>Resources/Raw</code> folder and from native storage if the app has written there, but not from the Images folder. The Images folder is &quot;hardcoded-designed for MAUI views&quot;. Place your images in <code>Resources/Raw</code> instead, subfolders are allowed.</p>
<p><strong>Q: How do I use my own SKBitmap or MAUI resource images with SkiaImage?</strong>
A: You have two options:</p>
<ol>
<li>Place images in <code>Resources/Raw</code> folder: <code>&lt;draw:SkiaImage Source=&quot;baboon.jpg&quot; /&gt;</code></li>
<li>Set images directly: <code>mySkiaImage.SetImageInternal(mySkImage)</code> or <code>mySkiaImage.SetBitmapInternal(mySkBitmap)</code></li>
</ol>
<h3 id="controls--gestures">Controls &amp; Gestures</h3>
<p><strong>Q: How do I prevent touch events from passing through overlapping controls?</strong>
A: Use the <code>BlockGesturesBelow=&quot;True&quot;</code> property on the top control. Note that <code>InputTransparent</code> makes the control itself avoid gestures, not controls below it in the Z-axis.</p>
<p><strong>Q: How can I enable mouse wheel scrolling in SkiaScroll?</strong>
A: Mouse wheel scrolling isn't built-in, but you can easily add it by subclassing SkiaScroll and overriding <code>ProcessGestures</code> to handle <code>TouchActionResult.Wheel</code> events. Check the discussions for a complete code example.</p>
<p><strong>Q: How do I maintain scroll position when reloading items in SkiaScroll?</strong>
A: Save the current <code>ViewportOffsetY</code> before reloading, then restore it after the new items are loaded using <code>ScrollTo(x, savedOffsetY, 0)</code>.</p>
<h3 id="advanced-usage">Advanced Usage</h3>
<p><strong>Q: Can I embed native MAUI controls inside DrawnUI?</strong>
A: Yes! Use <code>SkiaMauiElement</code> to embed native MAUI controls like WebView inside your DrawnUI canvas. This allows you to combine the best of both worlds.</p>
<p><strong>Q: How do I create custom controls with DrawnUI?</strong>
A: Inherit from <code>SkiaControl</code> for basic controls or <code>SkiaLayout</code> for container controls. Override the <code>Paint</code> method to define your custom drawing logic using SkiaSharp.</p>
<hr>
<h2 id="-need-help">🆘 Need Help?</h2>
<ul>
<li><strong><a href="fluent-extensions.html#troubleshooting">❓ Troubleshooting</a></strong> - Common issues and solutions</li>
<li><strong><a href="https://github.com/taublast/DrawnUi.Maui/issues">💬 GitHub Issues</a></strong> - Report bugs or ask questions</li>
<li><strong><a href="https://github.com/taublast/DrawnUi/discussions">🗨️ GitHub Discussions</a></strong> - Ask questions and get community help</li>
<li><strong><a href="https://taublast.github.io/posts/MauiJuly/">📖 Background Article</a></strong> - Why DrawnUI was created</li>
</ul>
<p><strong>Can't find the answer to your question?</strong> → <strong><a href="https://github.com/taublast/DrawnUi/discussions">Ask in GitHub Discussions</a></strong> - The community is here to help!</p>
<hr>
<p><strong>Ready to get started?</strong> → <strong><a href="getting-started.html">Install and Setup Guide</a></strong></p>

</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/master/docs/articles/index.md/#L1" class="edit-link">Edit this page</a>
        </div>

        <div class="next-article d-print-none border-top" id="nextArticle"></div>

      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
