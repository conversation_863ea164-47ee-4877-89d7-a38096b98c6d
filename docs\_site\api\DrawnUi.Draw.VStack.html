<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Class SkiaStack | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Class SkiaStack | DrawnUi Documentation ">
      
      <meta name="description" content="Vertical stack, like MAUI VerticalStackLayout">
      <link rel="icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_SkiaStack.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.SkiaStack%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/logo.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="DrawnUi.Draw.SkiaStack">



  <h1 id="DrawnUi_Draw_SkiaStack" data-uid="DrawnUi.Draw.SkiaStack" class="text-break">
Class SkiaStack  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Fluent/SkiaStack.cs/#L6"><i class="bi bi-code-slash"></i></a>
  </h1>

  <div class="facts text-secondary">
    <dl><dt>Namespace</dt><dd><a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Draw.html">Draw</a></dd></dl>
  <dl><dt>Assembly</dt><dd>DrawnUi.Maui.dll</dd></dl>
  </div>

  <div class="markdown summary"><p>Vertical stack, like MAUI VerticalStackLayout</p>
</div>
  <div class="markdown conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class SkiaStack : SkiaLayout, INotifyPropertyChanged, IEffectControlProvider, IToolTipElement, IContextFlyoutElement, IAnimatable, IVisualElementController, IElementController, IHotReloadableView, IReplaceableView, IView, IElement, ITransform, IReloadHandler, IVisualTreeElement, IContainer, IList&lt;IView&gt;, ICollection&lt;IView&gt;, IEnumerable&lt;IView&gt;, IEnumerable, IHasAfterEffects, ISkiaGestureListener, ISkiaGridLayout, ISkiaLayout, ISkiaControl, IDrawnBase, ICanBeUpdatedWithContext, ICanBeUpdated, ILayoutInsideViewport, IInsideViewport, IVisibilityAware, IDisposable</code></pre>
  </div>




  <dl class="typelist inheritance">
    <dt>Inheritance</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element">Element</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.styleableelement">StyleableElement</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.navigableelement">NavigableElement</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement">VisualElement</a></div>
      <div><a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></div>
      <div><a class="xref" href="DrawnUi.Draw.SkiaLayout.html">SkiaLayout</a></div>
      <div><span class="xref">SkiaStack</span></div>
    </dd>
  </dl>

  <dl class="typelist implements">
    <dt>Implements</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.componentmodel.inotifypropertychanged">INotifyPropertyChanged</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ieffectcontrolprovider">IEffectControlProvider</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.itooltipelement">IToolTipElement</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.icontextflyoutelement">IContextFlyoutElement</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ianimatable">IAnimatable</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ivisualelementcontroller">IVisualElementController</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ielementcontroller">IElementController</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.hotreload.ihotreloadableview">IHotReloadableView</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.ireplaceableview">IReplaceableView</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.iview">IView</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.ielement">IElement</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.itransform">ITransform</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.hotreload.ireloadhandler">IReloadHandler</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.ivisualtreeelement">IVisualTreeElement</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.icontainer">IContainer</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ilist-1">IList</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.iview">IView</a>&gt;</div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.icollection-1">ICollection</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.iview">IView</a>&gt;</div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1">IEnumerable</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.iview">IView</a>&gt;</div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.ienumerable">IEnumerable</a></div>
      <div><a class="xref" href="DrawnUi.Draw.IHasAfterEffects.html">IHasAfterEffects</a></div>
      <div><a class="xref" href="DrawnUi.Draw.ISkiaGestureListener.html">ISkiaGestureListener</a></div>
      <div><a class="xref" href="DrawnUi.Draw.ISkiaGridLayout.html">ISkiaGridLayout</a></div>
      <div><a class="xref" href="DrawnUi.Draw.ISkiaLayout.html">ISkiaLayout</a></div>
      <div><a class="xref" href="DrawnUi.Draw.ISkiaControl.html">ISkiaControl</a></div>
      <div><a class="xref" href="DrawnUi.Draw.IDrawnBase.html">IDrawnBase</a></div>
      <div><a class="xref" href="DrawnUi.Draw.ICanBeUpdatedWithContext.html">ICanBeUpdatedWithContext</a></div>
      <div><a class="xref" href="DrawnUi.Draw.ICanBeUpdated.html">ICanBeUpdated</a></div>
      <div><a class="xref" href="DrawnUi.Draw.ILayoutInsideViewport.html">ILayoutInsideViewport</a></div>
      <div><a class="xref" href="DrawnUi.Draw.IInsideViewport.html">IInsideViewport</a></div>
      <div><a class="xref" href="DrawnUi.Draw.IVisibilityAware.html">IVisibilityAware</a></div>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.idisposable">IDisposable</a></div>
    </dd>
  </dl>


  <dl class="typelist inheritedMembers">
    <dt>Inherited Members</dt>
    <dd>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_StackStructure">SkiaLayout.StackStructure</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_StackStructureMeasured">SkiaLayout.StackStructureMeasured</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_LatestStackStructure">SkiaLayout.LatestStackStructure</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_LatestMeasuredStackStructure">SkiaLayout.LatestMeasuredStackStructure</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_MeasureAndArrangeCell_SkiaSharp_SKRect_DrawnUi_Draw_ControlInStack_DrawnUi_Draw_SkiaControl_SkiaSharp_SKRect_System_Single_">SkiaLayout.MeasureAndArrangeCell(SKRect, ControlInStack, SkiaControl, SKRect, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_LayoutCell_DrawnUi_Draw_ScaledSize_DrawnUi_Draw_ControlInStack_DrawnUi_Draw_SkiaControl_SkiaSharp_SKRect_System_Single_">SkiaLayout.LayoutCell(ScaledSize, ControlInStack, SkiaControl, SKRect, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_GetSpacingForIndex_System_Int32_System_Single_">SkiaLayout.GetSpacingForIndex(int, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_GetSizeKey_SkiaSharp_SKSize_">SkiaLayout.GetSizeKey(SKSize)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_MeasureStack_SkiaSharp_SKRect_System_Single_">SkiaLayout.MeasureStack(SKRect, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_DrawStack_DrawnUi_Draw_DrawingContext_DrawnUi_Draw_LayoutStructure_">SkiaLayout.DrawStack(DrawingContext, LayoutStructure)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_PreArrange_SkiaSharp_SKRect_System_Single_System_Single_System_Single_">SkiaLayout.PreArrange(SKRect, float, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_IsGestureForChild_DrawnUi_Draw_SkiaControlWithRect_SkiaSharp_SKPoint_">SkiaLayout.IsGestureForChild(SkiaControlWithRect, SKPoint)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_ApplyBindingContext">SkiaLayout.ApplyBindingContext()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_ShouldInvalidateByChildren">SkiaLayout.ShouldInvalidateByChildren</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_OnTemplatesAvailable">SkiaLayout.OnTemplatesAvailable()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_SetMeasured_System_Single_System_Single_System_Boolean_System_Boolean_System_Single_">SkiaLayout.SetMeasured(float, float, bool, bool, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_RecyclingTemplateProperty">SkiaLayout.RecyclingTemplateProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_RecyclingTemplate">SkiaLayout.RecyclingTemplate</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_TemplatedHeaderProperty">SkiaLayout.TemplatedHeaderProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_TemplatedHeader">SkiaLayout.TemplatedHeader</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_TemplatedFooterProperty">SkiaLayout.TemplatedFooterProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_TemplatedFooter">SkiaLayout.TemplatedFooter</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_IsTemplated">SkiaLayout.IsTemplated</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_GetChildRect_System_Int32_">SkiaLayout.GetChildRect(int)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_GetChildRect_DrawnUi_Draw_ISkiaControl_">SkiaLayout.GetChildRect(ISkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_GetChildAt_System_Single_System_Single_">SkiaLayout.GetChildAt(float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_OnFocusChanged_System_Boolean_">SkiaLayout.OnFocusChanged(bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_VirtualizationProperty">SkiaLayout.VirtualizationProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_Virtualisation">SkiaLayout.Virtualisation</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_VirtualisationInflatedProperty">SkiaLayout.VirtualisationInflatedProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_VirtualisationInflated">SkiaLayout.VirtualisationInflated</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_RecyclingBufferProperty">SkiaLayout.RecyclingBufferProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_RecyclingBuffer">SkiaLayout.RecyclingBuffer</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_ChildrenGrid">SkiaLayout.ChildrenGrid</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_BreakLine">SkiaLayout.BreakLine()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_LineBreaks">SkiaLayout.LineBreaks</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_GetOnScreenVisibleArea_DrawnUi_Draw_DrawingContext_System_Numerics_Vector2_">SkiaLayout.GetOnScreenVisibleArea(DrawingContext, Vector2)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_DrawRenderObject_DrawnUi_Draw_DrawingContext_DrawnUi_Draw_CachedObject_">SkiaLayout.DrawRenderObject(DrawingContext, CachedObject)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout__emptyView">SkiaLayout._emptyView</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_SetupViews">SkiaLayout.SetupViews()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_IsEmpty">SkiaLayout.IsEmpty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_IsEmptyChanged">SkiaLayout.IsEmptyChanged</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_ApplyIsEmpty_System_Boolean_">SkiaLayout.ApplyIsEmpty(bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_CheckAndSetupIfEmpty">SkiaLayout.CheckAndSetupIfEmpty()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_DebugString">SkiaLayout.DebugString</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_ChildrenFactory">SkiaLayout.ChildrenFactory</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_SplitProperty">SkiaLayout.SplitProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_Split">SkiaLayout.Split</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_SplitAlignProperty">SkiaLayout.SplitAlignProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_SplitAlign">SkiaLayout.SplitAlign</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_SplitSpaceProperty">SkiaLayout.SplitSpaceProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_SplitSpace">SkiaLayout.SplitSpace</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_DynamicColumnsProperty">SkiaLayout.DynamicColumnsProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_DynamicColumns">SkiaLayout.DynamicColumns</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_OnViewportWasChanged_DrawnUi_Draw_ScaledRect_">SkiaLayout.OnViewportWasChanged(ScaledRect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_ViewportWasChanged">SkiaLayout.ViewportWasChanged</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_DrawChild_DrawnUi_Draw_DrawingContext_DrawnUi_Draw_ISkiaControl_">SkiaLayout.DrawChild(DrawingContext, ISkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_SetChildren_System_Collections_Generic_IEnumerable_DrawnUi_Draw_SkiaControl__">SkiaLayout.SetChildren(IEnumerable&lt;SkiaControl&gt;)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_OnMeasured">SkiaLayout.OnMeasured()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_InvalidateInternal">SkiaLayout.InvalidateInternal()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_templatesInvalidated">SkiaLayout.templatesInvalidated</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_InvalidateWithChildren">SkiaLayout.InvalidateWithChildren()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_InvalidateViewsList">SkiaLayout.InvalidateViewsList()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_ActualizeSubviews">SkiaLayout.ActualizeSubviews()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_GetTemplatesPoolPrefill">SkiaLayout.GetTemplatesPoolPrefill()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_GetTemplatesPoolLimit">SkiaLayout.GetTemplatesPoolLimit()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_OnChildrenChanged">SkiaLayout.OnChildrenChanged()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_Invalidate">SkiaLayout.Invalidate()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_CreateTemplatesInBackground">SkiaLayout.CreateTemplatesInBackground()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_MeasureAbsolute_SkiaSharp_SKRect_System_Single_">SkiaLayout.MeasureAbsolute(SKRect, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_lockMeasureLayout">SkiaLayout.lockMeasureLayout</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_MeasureLayout_DrawnUi_Draw_MeasureRequest_System_Boolean_">SkiaLayout.MeasureLayout(MeasureRequest, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout__lockTemplates">SkiaLayout._lockTemplates</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_Measure_System_Single_System_Single_System_Single_">SkiaLayout.Measure(float, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_ApplyMeasureResult">SkiaLayout.ApplyMeasureResult()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_InvalidatedChildrenInternal">SkiaLayout.InvalidatedChildrenInternal</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_Draw_DrawnUi_Draw_DrawingContext_">SkiaLayout.Draw(DrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_InvalidatedChildren">SkiaLayout.InvalidatedChildren</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_InvalidateByChild_DrawnUi_Draw_SkiaControl_">SkiaLayout.InvalidateByChild(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_Paint_DrawnUi_Draw_DrawingContext_">SkiaLayout.Paint(DrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_OnDisposing">SkiaLayout.OnDisposing()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_DrawViews_DrawnUi_Draw_DrawingContext_">SkiaLayout.DrawViews(DrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_IsStack">SkiaLayout.IsStack</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_TypeProperty">SkiaLayout.TypeProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_Type">SkiaLayout.Type</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_MeasureItemsStrategyProperty">SkiaLayout.MeasureItemsStrategyProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_MeasureItemsStrategy">SkiaLayout.MeasureItemsStrategy</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_ItemTemplatePoolSizeProperty">SkiaLayout.ItemTemplatePoolSizeProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_ItemTemplatePoolSize">SkiaLayout.ItemTemplatePoolSize</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_EmptyViewProperty">SkiaLayout.EmptyViewProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_EmptyView">SkiaLayout.EmptyView</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_ItemsSourceProperty">SkiaLayout.ItemsSourceProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_ItemsSource">SkiaLayout.ItemsSource</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_OnItemTemplateChanged">SkiaLayout.OnItemTemplateChanged()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_ApplyNewItemsSource">SkiaLayout.ApplyNewItemsSource</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_OnItemSourceChanged">SkiaLayout.OnItemSourceChanged()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_ResetItemsSource">SkiaLayout.ResetItemsSource()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_ResetScroll">SkiaLayout.ResetScroll()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_ItemsSourceCollectionChanged_System_Object_System_Collections_Specialized_NotifyCollectionChangedEventArgs_">SkiaLayout.ItemsSourceCollectionChanged(object, NotifyCollectionChangedEventArgs)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_RefreshAllViews">SkiaLayout.RefreshAllViews()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_GetCacheDebugInfo">SkiaLayout.GetCacheDebugInfo()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_OnPrintDebug">SkiaLayout.OnPrintDebug()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_OnLayoutReady">SkiaLayout.OnLayoutReady()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_OnAppearing">SkiaLayout.OnAppearing()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_OnDisappearing">SkiaLayout.OnDisappearing()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_OnAppeared">SkiaLayout.OnAppeared()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_OnDisappeared">SkiaLayout.OnDisappeared()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_OnLoaded">SkiaLayout.OnLoaded()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_GetVisibleChildIndexAt_SkiaSharp_SKPoint_">SkiaLayout.GetVisibleChildIndexAt(SKPoint)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_GetChildIndexAt_SkiaSharp_SKPoint_">SkiaLayout.GetChildIndexAt(SKPoint)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_MeasureGrid_SkiaSharp_SKRect_System_Single_">SkiaLayout.MeasureGrid(SKRect, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_DrawChildrenGrid_DrawnUi_Draw_DrawingContext_">SkiaLayout.DrawChildrenGrid(DrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_BuildGridLayout_SkiaSharp_SKSize_">SkiaLayout.BuildGridLayout(SKSize)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_GetColumn_Microsoft_Maui_Controls_BindableObject_">SkiaLayout.GetColumn(BindableObject)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_GetColumnSpan_Microsoft_Maui_Controls_BindableObject_">SkiaLayout.GetColumnSpan(BindableObject)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_GetRow_Microsoft_Maui_Controls_BindableObject_">SkiaLayout.GetRow(BindableObject)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_GetRowSpan_Microsoft_Maui_Controls_BindableObject_">SkiaLayout.GetRowSpan(BindableObject)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_SetColumn_Microsoft_Maui_Controls_BindableObject_System_Int32_">SkiaLayout.SetColumn(BindableObject, int)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_SetColumnSpan_Microsoft_Maui_Controls_BindableObject_System_Int32_">SkiaLayout.SetColumnSpan(BindableObject, int)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_SetRow_Microsoft_Maui_Controls_BindableObject_System_Int32_">SkiaLayout.SetRow(BindableObject, int)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_SetRowSpan_Microsoft_Maui_Controls_BindableObject_System_Int32_">SkiaLayout.SetRowSpan(BindableObject, int)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_GridStructure">SkiaLayout.GridStructure</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_GridStructureMeasured">SkiaLayout.GridStructureMeasured</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_DefaultColumnDefinitionProperty">SkiaLayout.DefaultColumnDefinitionProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_DefaultColumnDefinition">SkiaLayout.DefaultColumnDefinition</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_DefaultRowDefinitionProperty">SkiaLayout.DefaultRowDefinitionProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_DefaultRowDefinition">SkiaLayout.DefaultRowDefinition</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_RowSpacingProperty">SkiaLayout.RowSpacingProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_RowSpacing">SkiaLayout.RowSpacing</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_ColumnSpacingProperty">SkiaLayout.ColumnSpacingProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_ColumnSpacing">SkiaLayout.ColumnSpacing</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_ColumnDefinitionsProperty">SkiaLayout.ColumnDefinitionsProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_ColumnDefinitions">SkiaLayout.ColumnDefinitions</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_RowDefinitionsProperty">SkiaLayout.RowDefinitionsProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_RowDefinitions">SkiaLayout.RowDefinitions</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_UpdateSizeChangedHandlers_Microsoft_Maui_Controls_BindableObject_System_Object_System_Object_">SkiaLayout.UpdateSizeChangedHandlers(BindableObject, object, object)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_Invalidate_Microsoft_Maui_Controls_BindableObject_System_Object_System_Object_">SkiaLayout.Invalidate(BindableObject, object, object)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_UpdateRowColumnBindingContexts">SkiaLayout.UpdateRowColumnBindingContexts()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_MeasureList_SkiaSharp_SKRect_System_Single_">SkiaLayout.MeasureList(SKRect, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_FirstMeasuredIndex">SkiaLayout.FirstMeasuredIndex</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_LastMeasuredIndex">SkiaLayout.LastMeasuredIndex</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_FirstVisibleIndex">SkiaLayout.FirstVisibleIndex</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_LastVisibleIndex">SkiaLayout.LastVisibleIndex</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_DrawList_DrawnUi_Draw_DrawingContext_DrawnUi_Draw_LayoutStructure_">SkiaLayout.DrawList(DrawingContext, LayoutStructure)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_EstimatedTotalItems">SkiaLayout.EstimatedTotalItems</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_GetMeasuredContentEnd">SkiaLayout.GetMeasuredContentEnd()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_MeasureAdditionalItems_System_Int32_System_Int32_System_Single_">SkiaLayout.MeasureAdditionalItems(int, int, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_Clear">SkiaLayout.Clear()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_ReportHotreloadChildRemoved_DrawnUi_Draw_SkiaControl_">SkiaLayout.ReportHotreloadChildRemoved(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_ReportHotreloadChildAdded_DrawnUi_Draw_SkiaControl_">SkiaLayout.ReportHotreloadChildAdded(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_GetVisualChildren">SkiaLayout.GetVisualChildren()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_MeasureWrap_SkiaSharp_SKRect_System_Single_">SkiaLayout.MeasureWrap(SKRect, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_OnFirstDrawn">SkiaLayout.OnFirstDrawn()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaLayout.html#DrawnUi_Draw_SkiaLayout_PropagateVisibilityChanged_System_Boolean_">SkiaLayout.PropagateVisibilityChanged(bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetEnumerator">SkiaControl.GetEnumerator()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Add_Microsoft_Maui_IView_">SkiaControl.Add(IView)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Contains_Microsoft_Maui_IView_">SkiaControl.Contains(IView)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CopyTo_Microsoft_Maui_IView___System_Int32_">SkiaControl.CopyTo(IView[], int)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Remove_Microsoft_Maui_IView_">SkiaControl.Remove(IView)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Count">SkiaControl.Count</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsReadOnly">SkiaControl.IsReadOnly</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IndexOf_Microsoft_Maui_IView_">SkiaControl.IndexOf(IView)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Insert_System_Int32_Microsoft_Maui_IView_">SkiaControl.Insert(int, IView)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RemoveAt_System_Int32_">SkiaControl.RemoveAt(int)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Item_System_Int32_">SkiaControl.this[int]</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetVisualParent">SkiaControl.GetVisualParent()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_TransparentColor">SkiaControl.TransparentColor</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_WhiteColor">SkiaControl.WhiteColor</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_BlackColor">SkiaControl.BlackColor</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RedColor">SkiaControl.RedColor</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UsingControlStyle">SkiaControl.UsingControlStyle</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ClearColorProperty">SkiaControl.ClearColorProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ClearColor">SkiaControl.ClearColor</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnPropertyChanged_System_String_">SkiaControl.OnPropertyChanged(string)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddSubView_DrawnUi_Draw_SkiaControl_">SkiaControl.AddSubView(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RemoveSubView_DrawnUi_Draw_SkiaControl_">SkiaControl.RemoveSubView(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnLayoutChanged">SkiaControl.OnLayoutChanged()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SetupGradient_SkiaSharp_SKPaint_DrawnUi_Draw_SkiaGradient_SkiaSharp_SKRect_">SkiaControl.SetupGradient(SKPaint, SkiaGradient, SKRect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CreateShadow_DrawnUi_Draw_SkiaShadow_System_Single_">SkiaControl.CreateShadow(SkiaShadow, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UpdatePlatformShadow">SkiaControl.UpdatePlatformShadow()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PlatformShadow">SkiaControl.PlatformShadow</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_HasPlatformClip">SkiaControl.HasPlatformClip()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetDensity">SkiaControl.GetDensity()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_TransferState_Microsoft_Maui_IView_">SkiaControl.TransferState(IView)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Reload">SkiaControl.Reload()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ReloadHandler">SkiaControl.ReloadHandler</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UseCacheProperty">SkiaControl.UseCacheProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UseCache">SkiaControl.UseCache</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AllowCachingProperty">SkiaControl.AllowCachingProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AllowCaching">SkiaControl.AllowCaching</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RenderObject">SkiaControl.RenderObject</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnCacheCreated">SkiaControl.OnCacheCreated()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnCacheDestroyed">SkiaControl.OnCacheDestroyed()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CreatedCache">SkiaControl.CreatedCache</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DestroyRenderingObject">SkiaControl.DestroyRenderingObject()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DrawRenderObject_DrawnUi_Draw_DrawingContext_System_Single_System_Single_DrawnUi_Draw_CachedObject_">SkiaControl.DrawRenderObject(DrawingContext, float, float, CachedObject)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsRenderObjectValid_SkiaSharp_SKSize_DrawnUi_Draw_CachedObject_">SkiaControl.IsRenderObjectValid(SKSize, CachedObject)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CreateRenderedObject_DrawnUi_Draw_DrawingContext_SkiaSharp_SKRect_System_Boolean_">SkiaControl.CreateRenderedObject(DrawingContext, SKRect, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RasterizeToCache_DrawnUi_Draw_DrawingContext_">SkiaControl.RasterizeToCache(DrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CheckCachedObjectValid_DrawnUi_Draw_CachedObject_SkiaSharp_SKRect_DrawnUi_Draw_SkiaDrawingContext_">SkiaControl.CheckCachedObjectValid(CachedObject, SKRect, SkiaDrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UsingCacheType">SkiaControl.UsingCacheType</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CreateRenderingObject_DrawnUi_Draw_DrawingContext_SkiaSharp_SKRect_DrawnUi_Draw_CachedObject_DrawnUi_Draw_SkiaCacheType_System_Action_DrawnUi_Draw_DrawingContext__">SkiaControl.CreateRenderingObject(DrawingContext, SKRect, CachedObject, SkiaCacheType, Action&lt;DrawingContext&gt;)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DelegateDrawCache">SkiaControl.DelegateDrawCache</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DrawRenderObjectInternal_DrawnUi_Draw_DrawingContext_DrawnUi_Draw_CachedObject_">SkiaControl.DrawRenderObjectInternal(DrawingContext, CachedObject)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsCacheImage">SkiaControl.IsCacheImage</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsCacheOperations">SkiaControl.IsCacheOperations</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UseRenderingObject_DrawnUi_Draw_DrawingContext_SkiaSharp_SKRect_">SkiaControl.UseRenderingObject(DrawingContext, SKRect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CacheValidity">SkiaControl.CacheValidity</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetOffscreenRenderingAction">SkiaControl.GetOffscreenRenderingAction()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PushToOffscreenRendering_System_Action_System_Threading_CancellationToken_">SkiaControl.PushToOffscreenRendering(Action, CancellationToken)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_semaphoreOffsecreenProcess">SkiaControl.semaphoreOffsecreenProcess</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ProcessOffscreenCacheRenderingAsync">SkiaControl.ProcessOffscreenCacheRenderingAsync()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NeedUpdateFrontCache">SkiaControl.NeedUpdateFrontCache</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_InvalidateCache">SkiaControl.InvalidateCache()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_InvalidateCacheWithPrevious">SkiaControl.InvalidateCacheWithPrevious()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetCacheRecordingArea_SkiaSharp_SKRect_">SkiaControl.GetCacheRecordingArea(SKRect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetCacheArea_SkiaSharp_SKRect_">SkiaControl.GetCacheArea(SKRect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DrawUsingRenderObject_DrawnUi_Draw_DrawingContext_System_Single_System_Single_">SkiaControl.DrawUsingRenderObject(DrawingContext, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PrepareNode_DrawnUi_Draw_DrawingContext_System_Single_System_Single_">SkiaControl.PrepareNode(DrawingContext, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DrawDirectInternal_DrawnUi_Draw_DrawingContext_SkiaSharp_SKRect_">SkiaControl.DrawDirectInternal(DrawingContext, SKRect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CreateRenderingObjectAndPaint_DrawnUi_Draw_DrawingContext_SkiaSharp_SKRect_System_Action_DrawnUi_Draw_DrawingContext__">SkiaControl.CreateRenderingObjectAndPaint(DrawingContext, SKRect, Action&lt;DrawingContext&gt;)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AttachEffects">SkiaControl.AttachEffects()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnVisualEffectsChanged">SkiaControl.OnVisualEffectsChanged()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_EffectsGestureProcessors">SkiaControl.EffectsGestureProcessors</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_EffectsState">SkiaControl.EffectsState</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_EffectRenderers">SkiaControl.EffectRenderers</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_EffectImageFilter">SkiaControl.EffectImageFilter</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_EffectColorFilter">SkiaControl.EffectColorFilter</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_EffectPostRenderer">SkiaControl.EffectPostRenderer</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_VisualEffectsProperty">SkiaControl.VisualEffectsProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_VisualEffects">SkiaControl.VisualEffects</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DisableEffectsProperty">SkiaControl.DisableEffectsProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DisableEffects">SkiaControl.DisableEffects</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_InvalidateParents">SkiaControl.InvalidateParents()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_InvalidatedParent">SkiaControl.InvalidatedParent</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_InvalidateParent">SkiaControl.InvalidateParent()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_InvalidateViewport">SkiaControl.InvalidateViewport()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DirtyChildrenTracker">SkiaControl.DirtyChildrenTracker</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DirtyChildrenInternal">SkiaControl.DirtyChildrenInternal</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UpdateByChild_DrawnUi_Draw_SkiaControl_">SkiaControl.UpdateByChild(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_VisualLayer">SkiaControl.VisualLayer</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CachedImage">SkiaControl.CachedImage</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Left">SkiaControl.Left</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Top">SkiaControl.Top</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ControlStyleProperty">SkiaControl.ControlStyleProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ControlStyle">SkiaControl.ControlStyle</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ClippedEffectsWithProperty">SkiaControl.ClippedEffectsWithProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ClippedEffectsWith">SkiaControl.ClippedEffectsWith</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ClipWithProperty">SkiaControl.ClipWithProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ClipWith">SkiaControl.ClipWith</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ChildrenProperty">SkiaControl.ChildrenProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Children">SkiaControl.Children</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DefaultBlendMode">SkiaControl.DefaultBlendMode</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsVisibleInViewTree">SkiaControl.IsVisibleInViewTree()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetPositionOnCanvasInPoints">SkiaControl.GetPositionOnCanvasInPoints()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetFuturePositionOnCanvasInPoints_System_Boolean_">SkiaControl.GetFuturePositionOnCanvasInPoints(bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetPositionOnCanvas">SkiaControl.GetPositionOnCanvas()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetFuturePositionOnCanvas">SkiaControl.GetFuturePositionOnCanvas()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetSelfDrawingPosition">SkiaControl.GetSelfDrawingPosition()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_BuildSelfDrawingPosition_SkiaSharp_SKPoint_DrawnUi_Draw_SkiaControl_System_Boolean_">SkiaControl.BuildSelfDrawingPosition(SKPoint, SkiaControl, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_BuildDrawingOffsetRecursive_SkiaSharp_SKPoint_DrawnUi_Draw_SkiaControl_System_Boolean_System_Boolean_">SkiaControl.BuildDrawingOffsetRecursive(SKPoint, SkiaControl, bool, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_BuildDrawnOffsetRecursive_SkiaSharp_SKPoint_DrawnUi_Draw_SkiaControl_System_Boolean_System_Boolean_">SkiaControl.BuildDrawnOffsetRecursive(SKPoint, SkiaControl, bool, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CanDraw">SkiaControl.CanDraw</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SkipRendering">SkiaControl.SkipRendering</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DefaultContentCreated">SkiaControl.DefaultContentCreated</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CreateDefaultContent">SkiaControl.CreateDefaultContent()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SetDefaultMinimumContentSize_System_Double_System_Double_">SkiaControl.SetDefaultMinimumContentSize(double, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SetDefaultContentSize_System_Double_System_Double_">SkiaControl.SetDefaultContentSize(double, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GenerateParentChain">SkiaControl.GenerateParentChain()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SetVisualTransform_DrawnUi_Infrastructure_VisualTransform_">SkiaControl.SetVisualTransform(VisualTransform)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CommitInvalidations">SkiaControl.CommitInvalidations()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SuperViewChanged">SkiaControl.SuperViewChanged()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PostponeInvalidation_System_String_System_Action_">SkiaControl.PostponeInvalidation(string, Action)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetRenderingScaleFor_System_Single_System_Single_">SkiaControl.GetRenderingScaleFor(float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetRenderingScaleFor_System_Single_">SkiaControl.GetRenderingScaleFor(float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AnimateAsync_System_Action_System_Double__System_Action_System_Single_Microsoft_Maui_Easing_System_Threading_CancellationTokenSource_">SkiaControl.AnimateAsync(Action&lt;double&gt;, Action, float, Easing, CancellationTokenSource)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_FadeToAsync_System_Double_System_Single_Microsoft_Maui_Easing_System_Threading_CancellationTokenSource_">SkiaControl.FadeToAsync(double, float, Easing, CancellationTokenSource)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ScaleToAsync_System_Double_System_Double_System_Single_Microsoft_Maui_Easing_System_Threading_CancellationTokenSource_">SkiaControl.ScaleToAsync(double, double, float, Easing, CancellationTokenSource)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_TranslateToAsync_System_Double_System_Double_System_Single_Microsoft_Maui_Easing_System_Threading_CancellationTokenSource_">SkiaControl.TranslateToAsync(double, double, float, Easing, CancellationTokenSource)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RotateToAsync_System_Double_System_UInt32_Microsoft_Maui_Easing_System_Threading_CancellationTokenSource_">SkiaControl.RotateToAsync(double, uint, Easing, CancellationTokenSource)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PrintDebug_System_String_">SkiaControl.PrintDebug(string)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DebugRenderingProperty">SkiaControl.DebugRenderingProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DebugRendering">SkiaControl.DebugRendering</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AnimateRangeAsync_System_Action_System_Double__System_Double_System_Double_System_Double_Microsoft_Maui_Easing_System_Threading_CancellationToken_System_Boolean_">SkiaControl.AnimateRangeAsync(Action&lt;double&gt;, double, double, double, Easing, CancellationToken, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NotValidPoint">SkiaControl.NotValidPoint()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PointIsValid_SkiaSharp_SKPoint_">SkiaControl.PointIsValid(SKPoint)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SizeRequest">SkiaControl.SizeRequest</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AdaptWidthConstraintToRequest_System_Single_Microsoft_Maui_Thickness_System_Double_">SkiaControl.AdaptWidthConstraintToRequest(float, Thickness, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AdaptHeightContraintToRequest_System_Single_Microsoft_Maui_Thickness_System_Double_">SkiaControl.AdaptHeightContraintToRequest(float, Thickness, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetMeasuringConstraints_DrawnUi_Draw_MeasureRequest_">SkiaControl.GetMeasuringConstraints(MeasureRequest)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AdaptConstraintToContentRequest_System_Single_System_Double_System_Double_System_Boolean_System_Double_System_Double_System_Single_System_Boolean_">SkiaControl.AdaptConstraintToContentRequest(float, double, double, bool, double, double, float, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AdaptWidthConstraintToContentRequest_DrawnUi_Infrastructure_MeasuringConstraints_System_Single_System_Boolean_">SkiaControl.AdaptWidthConstraintToContentRequest(MeasuringConstraints, float, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AdaptHeightConstraintToContentRequest_DrawnUi_Infrastructure_MeasuringConstraints_System_Single_System_Boolean_">SkiaControl.AdaptHeightConstraintToContentRequest(MeasuringConstraints, float, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AdaptWidthConstraintToContentRequest_System_Single_DrawnUi_Draw_ScaledSize_System_Double_">SkiaControl.AdaptWidthConstraintToContentRequest(float, ScaledSize, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AdaptHeightConstraintToContentRequest_System_Single_DrawnUi_Draw_ScaledSize_System_Double_">SkiaControl.AdaptHeightConstraintToContentRequest(float, ScaledSize, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AdaptToContraints_SkiaSharp_SKRect_System_Double_System_Double_System_Double_System_Double_">SkiaControl.AdaptToContraints(SKRect, double, double, double, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AdaptSizeRequestToContent_System_Double_System_Double_">SkiaControl.AdaptSizeRequestToContent(double, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetTopParentView">SkiaControl.GetTopParentView()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetParentElement_DrawnUi_Draw_IDrawnBase_">SkiaControl.GetParentElement(IDrawnBase)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GestureIsInside_AppoMobi_Maui_Gestures_TouchActionEventArgs_System_Single_System_Single_">SkiaControl.GestureIsInside(TouchActionEventArgs, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GestureStartedInside_AppoMobi_Maui_Gestures_TouchActionEventArgs_System_Single_System_Single_">SkiaControl.GestureStartedInside(TouchActionEventArgs, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsPointInside_System_Single_System_Single_System_Single_">SkiaControl.IsPointInside(float, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsPointInside_SkiaSharp_SKRect_System_Single_System_Single_System_Single_">SkiaControl.IsPointInside(SKRect, float, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsPixelInside_SkiaSharp_SKRect_System_Single_System_Single_">SkiaControl.IsPixelInside(SKRect, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsPixelInside_System_Single_System_Single_">SkiaControl.IsPixelInside(float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CheckGestureIsInsideChild_DrawnUi_Draw_SkiaControl_AppoMobi_Maui_Gestures_TouchActionEventArgs_System_Single_System_Single_">SkiaControl.CheckGestureIsInsideChild(SkiaControl, TouchActionEventArgs, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CheckGestureIsForChild_DrawnUi_Draw_SkiaControl_AppoMobi_Maui_Gestures_TouchActionEventArgs_System_Single_System_Single_">SkiaControl.CheckGestureIsForChild(SkiaControl, TouchActionEventArgs, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LockIterateListeners">SkiaControl.LockIterateListeners</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LockChildrenGesturesProperty">SkiaControl.LockChildrenGesturesProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LockChildrenGestures">SkiaControl.LockChildrenGestures</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CheckChildrenGesturesLocked_AppoMobi_Maui_Gestures_TouchActionResult_">SkiaControl.CheckChildrenGesturesLocked(TouchActionResult)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnSkiaGestureEvent_DrawnUi_Draw_SkiaGesturesParameters_DrawnUi_Draw_GestureEventProcessingInfo_">SkiaControl.OnSkiaGestureEvent(SkiaGesturesParameters, GestureEventProcessingInfo)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsGestureForChild_DrawnUi_Draw_SkiaControl_DrawnUi_Draw_SkiaGesturesParameters_">SkiaControl.IsGestureForChild(SkiaControl, SkiaGesturesParameters)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CommandChildTappedProperty">SkiaControl.CommandChildTappedProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CommandChildTapped">SkiaControl.CommandChildTapped</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Tapped">SkiaControl.Tapped</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_TouchEffectColorProperty">SkiaControl.TouchEffectColorProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_TouchEffectColor">SkiaControl.TouchEffectColor</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AnimationTappedProperty">SkiaControl.AnimationTappedProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AnimationTapped">SkiaControl.AnimationTapped</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_TransformViewProperty">SkiaControl.TransformViewProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_TransformView">SkiaControl.TransformView</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SendTapped_System_Object_DrawnUi_Draw_SkiaGesturesParameters_DrawnUi_Draw_GestureEventProcessingInfo_System_Boolean_">SkiaControl.SendTapped(object, SkiaGesturesParameters, GestureEventProcessingInfo, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ChildTapped">SkiaControl.ChildTapped</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_TransformPointToLocalSpace_SkiaSharp_SKPoint_">SkiaControl.TransformPointToLocalSpace(SKPoint)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsGestureInside_DrawnUi_Draw_GestureEventProcessingInfo_">SkiaControl.IsGestureInside(GestureEventProcessingInfo)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnGestures">SkiaControl.OnGestures</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ProcessGestures_DrawnUi_Draw_SkiaGesturesParameters_DrawnUi_Draw_GestureEventProcessingInfo_">SkiaControl.ProcessGestures(SkiaGesturesParameters, GestureEventProcessingInfo)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_BlockGesturesBelowProperty">SkiaControl.BlockGesturesBelowProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_BlockGesturesBelow">SkiaControl.BlockGesturesBelow</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UpdateLocks">SkiaControl.UpdateLocks</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UnlockUpdate">SkiaControl.UnlockUpdate()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LockUpdate_System_Boolean_">SkiaControl.LockUpdate(bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NativeParent">SkiaControl.NativeParent</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ParentProperty">SkiaControl.ParentProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Parent">SkiaControl.Parent</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AlignContentVerticalProperty">SkiaControl.AlignContentVerticalProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AlignContentVertical">SkiaControl.AlignContentVertical</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AlignContentHorizontalProperty">SkiaControl.AlignContentHorizontalProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AlignContentHorizontal">SkiaControl.AlignContentHorizontal</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_VerticalOptionsProperty">SkiaControl.VerticalOptionsProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_VerticalOptions">SkiaControl.VerticalOptions</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_HorizontalOptionsProperty">SkiaControl.HorizontalOptionsProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_HorizontalOptions">SkiaControl.HorizontalOptions</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnParentVisibilityChanged_System_Boolean_">SkiaControl.OnParentVisibilityChanged(bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_VisibilityChanged">SkiaControl.VisibilityChanged</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SendVisibilityChanged">SkiaControl.SendVisibilityChanged()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnVisibilityChanged_System_Boolean_">SkiaControl.OnVisibilityChanged(bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_lockPausingAnimators">SkiaControl.lockPausingAnimators</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PauseAllAnimators">SkiaControl.PauseAllAnimators()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ResumePausedAnimators">SkiaControl.ResumePausedAnimators()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsGhostProperty">SkiaControl.IsGhostProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsGhost">SkiaControl.IsGhost</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IgnoreChildrenInvalidationsProperty">SkiaControl.IgnoreChildrenInvalidationsProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IgnoreChildrenInvalidations">SkiaControl.IgnoreChildrenInvalidations</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_FillGradientProperty">SkiaControl.FillGradientProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_FillGradient">SkiaControl.FillGradient</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_HasFillGradient">SkiaControl.HasFillGradient</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetSizeRequest_System_Single_System_Single_System_Boolean_">SkiaControl.GetSizeRequest(float, float, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SmartMax_System_Single_System_Single_">SkiaControl.SmartMax(float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SmartMin_System_Single_System_Single_">SkiaControl.SmartMin(float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ViewportHeightLimitProperty">SkiaControl.ViewportHeightLimitProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ViewportHeightLimit">SkiaControl.ViewportHeightLimit</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ViewportWidthLimitProperty">SkiaControl.ViewportWidthLimitProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ViewportWidthLimit">SkiaControl.ViewportWidthLimit</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Height">SkiaControl.Height</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Width">SkiaControl.Width</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Scale">SkiaControl.Scale</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_TagProperty">SkiaControl.TagProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Tag">SkiaControl.Tag</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LockRatioProperty">SkiaControl.LockRatioProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LockRatio">SkiaControl.LockRatio</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_HeightRequestRatioProperty">SkiaControl.HeightRequestRatioProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_HeightRequestRatio">SkiaControl.HeightRequestRatio</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_WidthRequestRatioProperty">SkiaControl.WidthRequestRatioProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_WidthRequestRatio">SkiaControl.WidthRequestRatio</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_HorizontalFillRatioProperty">SkiaControl.HorizontalFillRatioProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_HorizontalFillRatio">SkiaControl.HorizontalFillRatio</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_VerticalFillRatioProperty">SkiaControl.VerticalFillRatioProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_VerticalFillRatio">SkiaControl.VerticalFillRatio</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_HorizontalPositionOffsetRatioProperty">SkiaControl.HorizontalPositionOffsetRatioProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_HorizontalPositionOffsetRatio">SkiaControl.HorizontalPositionOffsetRatio</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_VerticalPositionOffsetRatioProperty">SkiaControl.VerticalPositionOffsetRatioProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_VerticalPositionOffsetRatio">SkiaControl.VerticalPositionOffsetRatio</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_FillBlendModeProperty">SkiaControl.FillBlendModeProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_FillBlendMode">SkiaControl.FillBlendMode</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SkewXProperty">SkiaControl.SkewXProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SkewX">SkiaControl.SkewX</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SkewYProperty">SkiaControl.SkewYProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SkewY">SkiaControl.SkewY</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_TranslationZProperty">SkiaControl.TranslationZProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_TranslationZ">SkiaControl.TranslationZ</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RotationZProperty">SkiaControl.RotationZProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RotationZ">SkiaControl.RotationZ</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Perspective1Property">SkiaControl.Perspective1Property</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Perspective1">SkiaControl.Perspective1</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Perspective2Property">SkiaControl.Perspective2Property</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Perspective2">SkiaControl.Perspective2</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ParentChanged">SkiaControl.ParentChanged</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AdjustClippingProperty">SkiaControl.AdjustClippingProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AdjustClipping">SkiaControl.AdjustClipping</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PaddingProperty">SkiaControl.PaddingProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Padding">SkiaControl.Padding</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_MarginProperty">SkiaControl.MarginProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Margin">SkiaControl.Margin</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddMarginTopProperty">SkiaControl.AddMarginTopProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddMarginTop">SkiaControl.AddMarginTop</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddMarginBottomProperty">SkiaControl.AddMarginBottomProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddMarginBottom">SkiaControl.AddMarginBottom</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddMarginLeftProperty">SkiaControl.AddMarginLeftProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddMarginLeft">SkiaControl.AddMarginLeft</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddMarginRightProperty">SkiaControl.AddMarginRightProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddMarginRight">SkiaControl.AddMarginRight</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Margins">SkiaControl.Margins</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SpacingProperty">SkiaControl.SpacingProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Spacing">SkiaControl.Spacing</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddTranslationYProperty">SkiaControl.AddTranslationYProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddTranslationY">SkiaControl.AddTranslationY</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddTranslationXProperty">SkiaControl.AddTranslationXProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddTranslationX">SkiaControl.AddTranslationX</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ExpandDirtyRegionProperty">SkiaControl.ExpandDirtyRegionProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ExpandDirtyRegion">SkiaControl.ExpandDirtyRegion</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LockFocusProperty">SkiaControl.LockFocusProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LockFocus">SkiaControl.LockFocus</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsClippedToBoundsProperty">SkiaControl.IsClippedToBoundsProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsClippedToBounds">SkiaControl.IsClippedToBounds</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ClipEffectsProperty">SkiaControl.ClipEffectsProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ClipEffects">SkiaControl.ClipEffects</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_BindableTriggerProperty">SkiaControl.BindableTriggerProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnTriggerChanged">SkiaControl.OnTriggerChanged()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_BindableTrigger">SkiaControl.BindableTrigger</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Value1Property">SkiaControl.Value1Property</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Value1">SkiaControl.Value1</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Value2Property">SkiaControl.Value2Property</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Value2">SkiaControl.Value2</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Value3Property">SkiaControl.Value3Property</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Value3">SkiaControl.Value3</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Value4Property">SkiaControl.Value4Property</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Value4">SkiaControl.Value4</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RenderingScaleProperty">SkiaControl.RenderingScaleProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RenderingScale">SkiaControl.RenderingScale</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RenderedAtDestination">SkiaControl.RenderedAtDestination</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnScaleChanged">SkiaControl.OnScaleChanged()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SetPropertyValue_Microsoft_Maui_Controls_BindableProperty_System_Object_">SkiaControl.SetPropertyValue(BindableProperty, object)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Uid">SkiaControl.Uid</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DegreesToRadians_System_Single_">SkiaControl.DegreesToRadians(float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RadiansToDegrees_System_Single_">SkiaControl.RadiansToDegrees(float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DegreesToRadians_System_Double_">SkiaControl.DegreesToRadians(double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RadiansToDegrees_System_Double_">SkiaControl.RadiansToDegrees(double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LinearGradientAngleToPoints_System_Double_">SkiaControl.LinearGradientAngleToPoints(double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DisposalDelay">SkiaControl.DisposalDelay</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsAlive">SkiaControl.IsAlive</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DisposeObject">SkiaControl.DisposeObject()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DisposeObject_System_IDisposable_">SkiaControl.DisposeObject(IDisposable)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnSizeChanged">SkiaControl.OnSizeChanged()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Clipping">SkiaControl.Clipping</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Hero">SkiaControl.Hero</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ContextIndex">SkiaControl.ContextIndex</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsRootView">SkiaControl.IsRootView()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DefineAvailableSize_SkiaSharp_SKRect_System_Single_System_Single_System_Single_System_Boolean_">SkiaControl.DefineAvailableSize(SKRect, float, float, float, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsLayoutDirty">SkiaControl.IsLayoutDirty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SnapToPixel_System_Double_System_Double_">SkiaControl.SnapToPixel(double, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SnapSizeToPixel_SkiaSharp_SKSize_System_Double_">SkiaControl.SnapSizeToPixel(SKSize, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SnapPointToPixel_SkiaSharp_SKPoint_System_Double_">SkiaControl.SnapPointToPixel(SKPoint, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SnapRectCenter_SkiaSharp_SKRect_System_Single_">SkiaControl.SnapRectCenter(SKRect, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SnapRectToPixels_SkiaSharp_SKRect_System_Single_">SkiaControl.SnapRectToPixels(SKRect, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RoundCenterAlignment">SkiaControl.RoundCenterAlignment</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CalculateLayout_SkiaSharp_SKRect_System_Single_System_Single_System_Single_">SkiaControl.CalculateLayout(SKRect, float, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ContentSize">SkiaControl.ContentSize</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_WasMeasured">SkiaControl.WasMeasured</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnDrawingSizeChanged">SkiaControl.OnDrawingSizeChanged()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AdaptCachedLayout_SkiaSharp_SKRect_System_Single_">SkiaControl.AdaptCachedLayout(SKRect, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl__lastMeasuredForWidth">SkiaControl._lastMeasuredForWidth</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl__lastMeasuredForHeight">SkiaControl._lastMeasuredForHeight</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DrawingRect">SkiaControl.DrawingRect</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DirtyRegion">SkiaControl.DirtyRegion</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_HitIsInside_System_Single_System_Single_">SkiaControl.HitIsInside(float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_HitBoxAuto">SkiaControl.HitBoxAuto</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsGestureForChild_DrawnUi_Draw_ISkiaGestureListener_SkiaSharp_SKPoint_">SkiaControl.IsGestureForChild(ISkiaGestureListener, SKPoint)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsGestureForChild_DrawnUi_Draw_ISkiaGestureListener_System_Single_System_Single_">SkiaControl.IsGestureForChild(ISkiaGestureListener, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ApplyTransforms_SkiaSharp_SKRect_">SkiaControl.ApplyTransforms(SKRect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RenderTransformMatrix">SkiaControl.RenderTransformMatrix</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_InverseRenderTransformMatrix">SkiaControl.InverseRenderTransformMatrix</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ApplyTransformationMatrix_DrawnUi_Draw_SkiaDrawingContext_">SkiaControl.ApplyTransformationMatrix(SkiaDrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CreateTransformationMatrix_DrawnUi_Draw_SkiaDrawingContext_SkiaSharp_SKRect_">SkiaControl.CreateTransformationMatrix(SkiaDrawingContext, SKRect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ApplyTransforms_DrawnUi_Draw_SkiaDrawingContext_SkiaSharp_SKRect_">SkiaControl.ApplyTransforms(SkiaDrawingContext, SKRect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_TranslateInputDirectOffsetToPoints_Microsoft_Maui_Graphics_PointF_SkiaSharp_SKPoint_">SkiaControl.TranslateInputDirectOffsetToPoints(PointF, SKPoint)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_TranslateInputOffsetToPixels_Microsoft_Maui_Graphics_PointF_SkiaSharp_SKPoint_">SkiaControl.TranslateInputOffsetToPixels(PointF, SKPoint)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_TranslateInputCoords_SkiaSharp_SKPoint_System_Boolean_">SkiaControl.TranslateInputCoords(SKPoint, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CalculatePositionOffset_System_Boolean_System_Boolean_System_Boolean_">SkiaControl.CalculatePositionOffset(bool, bool, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CalculateFuturePositionOffset_System_Boolean_System_Boolean_System_Boolean_">SkiaControl.CalculateFuturePositionOffset(bool, bool, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ArrangedDestination">SkiaControl.ArrangedDestination</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LayoutIsReady">SkiaControl.LayoutIsReady</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Disposing">SkiaControl.Disposing</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsLayoutReady">SkiaControl.IsLayoutReady</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LayoutReady">SkiaControl.LayoutReady</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CheckIsGhost">SkiaControl.CheckIsGhost()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Layout_DrawnUi_Draw_DrawingContext_SkiaSharp_SKRect_">SkiaControl.Layout(DrawingContext, SKRect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Layout_DrawnUi_Draw_DrawingContext_System_Single_System_Single_System_Single_System_Single_">SkiaControl.Layout(DrawingContext, float, float, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Layout_DrawnUi_Draw_DrawingContext_System_Int32_System_Int32_System_Int32_System_Int32_">SkiaControl.Layout(DrawingContext, int, int, int, int)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Arrange_SkiaSharp_SKRect_System_Single_System_Single_System_Single_">SkiaControl.Arrange(SKRect, float, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PostArrange_SkiaSharp_SKRect_System_Single_System_Single_System_Single_">SkiaControl.PostArrange(SKRect, float, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_MeasureChild_DrawnUi_Draw_SkiaControl_System_Double_System_Double_System_Single_">SkiaControl.MeasureChild(SkiaControl, double, double, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_MeasureContent_System_Collections_Generic_IEnumerable_DrawnUi_Draw_SkiaControl__SkiaSharp_SKRect_System_Single_">SkiaControl.MeasureContent(IEnumerable&lt;SkiaControl&gt;, SKRect, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SetInheritedBindingContext_System_Object_">SkiaControl.SetInheritedBindingContext(object)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ApplyingBindingContext">SkiaControl.ApplyingBindingContext</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_BindingContextWasSet">SkiaControl.BindingContextWasSet</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnBindingContextChanged">SkiaControl.OnBindingContextChanged()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_MeasureInternal_DrawnUi_Draw_MeasureRequest_">SkiaControl.MeasureInternal(MeasureRequest)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_InitializeDefaultContent_System_Boolean_">SkiaControl.InitializeDefaultContent(bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SetMeasuredAsEmpty_System_Single_">SkiaControl.SetMeasuredAsEmpty(float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetContentSizeForAutosizeInPixels">SkiaControl.GetContentSizeForAutosizeInPixels()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SetMeasuredAdaptToContentSize_DrawnUi_Infrastructure_MeasuringConstraints_System_Single_">SkiaControl.SetMeasuredAdaptToContentSize(MeasuringConstraints, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetSizeInPoints_SkiaSharp_SKSize_System_Single_">SkiaControl.GetSizeInPoints(SKSize, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CreateMeasureRequest_System_Single_System_Single_System_Single_">SkiaControl.CreateMeasureRequest(float, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetMeasuringRectForChildren_System_Single_System_Single_System_Double_">SkiaControl.GetMeasuringRectForChildren(float, float, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_MeasureAbsoluteBase_SkiaSharp_SKRect_System_Single_">SkiaControl.MeasureAbsoluteBase(SKRect, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ContractPixelsRect_SkiaSharp_SKRect_System_Single_Microsoft_Maui_Thickness_">SkiaControl.ContractPixelsRect(SKRect, float, Thickness)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ExpandPixelsRect_SkiaSharp_SKRect_System_Single_Microsoft_Maui_Thickness_">SkiaControl.ExpandPixelsRect(SKRect, float, Thickness)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetDrawingRectForChildren_SkiaSharp_SKRect_System_Double_">SkiaControl.GetDrawingRectForChildren(SKRect, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetDrawingRectWithMargins_SkiaSharp_SKRect_System_Double_">SkiaControl.GetDrawingRectWithMargins(SKRect, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_lockMeasured">SkiaControl.lockMeasured</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsMeasuring">SkiaControl.IsMeasuring</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LockMeasure">SkiaControl.LockMeasure</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SendOnMeasured">SkiaControl.SendOnMeasured()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Measured">SkiaControl.Measured</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_MeasuredSize">SkiaControl.MeasuredSize</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NeedAutoSize">SkiaControl.NeedAutoSize</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NeedAutoHeight">SkiaControl.NeedAutoHeight</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NeedAutoWidth">SkiaControl.NeedAutoWidth</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsDisposed">SkiaControl.IsDisposed</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsDisposing">SkiaControl.IsDisposing</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NeedDispose">SkiaControl.NeedDispose</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_FindView__1_System_String_">SkiaControl.FindView&lt;TChild&gt;(string)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_FindView__1">SkiaControl.FindView&lt;TChild&gt;()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_FindViewByTag_System_String_">SkiaControl.FindViewByTag(string)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ExecuteUponDisposal">SkiaControl.ExecuteUponDisposal</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ExecuteAfterCreated">SkiaControl.ExecuteAfterCreated</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ExecuteOnPaint">SkiaControl.ExecuteOnPaint</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ThrowIfDisposed">SkiaControl.ThrowIfDisposed()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Dispose_System_Boolean_">SkiaControl.Dispose(bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Dispose">SkiaControl.Dispose()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PaintErase">SkiaControl.PaintErase</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetNanoseconds">SkiaControl.GetNanoseconds()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnBeforeMeasure">SkiaControl.OnBeforeMeasure()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OptionalOnBeforeDrawing">SkiaControl.OptionalOnBeforeDrawing()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsOverlay">SkiaControl.IsOverlay</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PostAnimators">SkiaControl.PostAnimators</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UpdateWhenReturnedFromBackgroundProperty">SkiaControl.UpdateWhenReturnedFromBackgroundProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UpdateWhenReturnedFromBackground">SkiaControl.UpdateWhenReturnedFromBackground</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnSuperviewShouldRenderChanged_System_Boolean_">SkiaControl.OnSuperviewShouldRenderChanged(bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsRendering">SkiaControl.IsRendering</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NodeAttached">SkiaControl.NodeAttached</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_FindRenderedNode_DrawnUi_Draw_SkiaControl_">SkiaControl.FindRenderedNode(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CreateRenderedNode_SkiaSharp_SKRect_System_Single_">SkiaControl.CreateRenderedNode(SKRect, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Render_DrawnUi_Draw_DrawingContext_">SkiaControl.Render(DrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Rendered">SkiaControl.Rendered</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LockDraw">SkiaControl.LockDraw</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LockRenderObject">SkiaControl.LockRenderObject</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddPaintArguments_DrawnUi_Draw_DrawingContext_">SkiaControl.AddPaintArguments(DrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnBeforeDrawing_DrawnUi_Draw_DrawingContext_">SkiaControl.OnBeforeDrawing(DrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnAfterDrawing_DrawnUi_Draw_DrawingContext_">SkiaControl.OnAfterDrawing(DrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_X">SkiaControl.X</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Y">SkiaControl.Y</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_FinalizeDrawingWithRenderObject_DrawnUi_Draw_DrawingContext_">SkiaControl.FinalizeDrawingWithRenderObject(DrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetPositionOffsetInPoints">SkiaControl.GetPositionOffsetInPoints()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetPositionOffsetInPixels_System_Boolean_System_Boolean_System_Boolean_">SkiaControl.GetPositionOffsetInPixels(bool, bool, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetFuturePositionOffsetInPixels_System_Boolean_System_Boolean_System_Boolean_">SkiaControl.GetFuturePositionOffsetInPixels(bool, bool, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetOffsetInsideControlInPoints_Microsoft_Maui_Graphics_PointF_SkiaSharp_SKPoint_">SkiaControl.GetOffsetInsideControlInPoints(PointF, SKPoint)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetOffsetInsideControlInPixels_Microsoft_Maui_Graphics_PointF_SkiaSharp_SKPoint_">SkiaControl.GetOffsetInsideControlInPixels(PointF, SKPoint)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LastDrawnAt">SkiaControl.LastDrawnAt</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ExecutePostAnimators_DrawnUi_Draw_DrawingContext_">SkiaControl.ExecutePostAnimators(DrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Repaint">SkiaControl.Repaint()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl__paintWithEffects">SkiaControl._paintWithEffects</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl__paintWithOpacity">SkiaControl._paintWithOpacity</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CustomizeLayerPaint">SkiaControl.CustomizeLayerPaint</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Helper3d">SkiaControl.Helper3d</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DrawWithClipAndTransforms_DrawnUi_Draw_DrawingContext_SkiaSharp_SKRect_System_Boolean_System_Boolean_System_Action_DrawnUi_Draw_DrawingContext__">SkiaControl.DrawWithClipAndTransforms(DrawingContext, SKRect, bool, bool, Action&lt;DrawingContext&gt;)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsSimpleRectangle_SkiaSharp_SKPath_">SkiaControl.IsSimpleRectangle(SKPath)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ClipSmart_SkiaSharp_SKCanvas_SkiaSharp_SKPath_SkiaSharp_SKClipOperation_">SkiaControl.ClipSmart(SKCanvas, SKPath, SKClipOperation)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ShouldClipAntialiased">SkiaControl.ShouldClipAntialiased</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NeedMeasure">SkiaControl.NeedMeasure</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SafePostAction_System_Action_">SkiaControl.SafePostAction(Action)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SafeAction_System_Action_">SkiaControl.SafeAction(Action)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NeedRemeasuring">SkiaControl.NeedRemeasuring</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PaintWithShadows_DrawnUi_Draw_DrawingContext_System_Action_">SkiaControl.PaintWithShadows(DrawingContext, Action)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PaintWithEffects_DrawnUi_Draw_DrawingContext_">SkiaControl.PaintWithEffects(DrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_WasFirstTimeDrawn">SkiaControl.WasFirstTimeDrawn</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CreateClip_System_Object_System_Boolean_SkiaSharp_SKPath_">SkiaControl.CreateClip(object, bool, SKPath)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DebugRenderingColor">SkiaControl.DebugRenderingColor</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UseTranslationY">SkiaControl.UseTranslationY</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UseTranslationX">SkiaControl.UseTranslationX</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RenderViewsList_DrawnUi_Draw_DrawingContext_System_Collections_Generic_IEnumerable_DrawnUi_Draw_SkiaControl__">SkiaControl.RenderViewsList(DrawingContext, IEnumerable&lt;SkiaControl&gt;)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl__measuredStamp">SkiaControl._measuredStamp</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl__builtRenderTreeStamp">SkiaControl._builtRenderTreeStamp</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RenderTree">SkiaControl.RenderTree</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SetRenderingTree_System_Collections_Generic_List_DrawnUi_Draw_SkiaControlWithRect__">SkiaControl.SetRenderingTree(List&lt;SkiaControlWithRect&gt;)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Invalidated">SkiaControl.Invalidated</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NeedUpdate">SkiaControl.NeedUpdate</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Superview">SkiaControl.Superview</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DelegateGetOnScreenVisibleArea">SkiaControl.DelegateGetOnScreenVisibleArea</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsParentIndependent">SkiaControl.IsParentIndependent</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_WillNotUpdateParent">SkiaControl.WillNotUpdateParent</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UpdateInternal">SkiaControl.UpdateInternal()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Update">SkiaControl.Update()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Updated">SkiaControl.Updated</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_StreamFromString_System_String_">SkiaControl.StreamFromString(string)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DeviceUnitsToPixels_System_Double_">SkiaControl.DeviceUnitsToPixels(double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PixelsToDeviceUnits_System_Double_">SkiaControl.PixelsToDeviceUnits(double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PaintSystem">SkiaControl.PaintSystem</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SetupBackgroundPaint_SkiaSharp_SKPaint_SkiaSharp_SKRect_">SkiaControl.SetupBackgroundPaint(SKPaint, SKRect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PaintTintBackground_SkiaSharp_SKCanvas_SkiaSharp_SKRect_">SkiaControl.PaintTintBackground(SKCanvas, SKRect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CombineClipping_SkiaSharp_SKPath_SkiaSharp_SKPath_">SkiaControl.CombineClipping(SKPath, SKPath)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ActionWithClipping_SkiaSharp_SKRect_SkiaSharp_SKCanvas_System_Action_">SkiaControl.ActionWithClipping(SKRect, SKCanvas, Action)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CalculateMargins">SkiaControl.CalculateMargins()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetAllMarginsInPixels_System_Single_">SkiaControl.GetAllMarginsInPixels(float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetMarginsInPixels_System_Single_">SkiaControl.GetMarginsInPixels(float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_InvalidateMeasureInternal">SkiaControl.InvalidateMeasureInternal()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CalculateSizeRequest">SkiaControl.CalculateSizeRequest()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_InvalidateChildrenTree_DrawnUi_Draw_SkiaControl_">SkiaControl.InvalidateChildrenTree(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_InvalidateChildrenTree">SkiaControl.InvalidateChildrenTree()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OutputDebug">SkiaControl.OutputDebug</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_WillInvalidateMeasure">SkiaControl.WillInvalidateMeasure</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NeedInvalidateMeasure_Microsoft_Maui_Controls_BindableObject_System_Object_System_Object_">SkiaControl.NeedInvalidateMeasure(BindableObject, object, object)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NeedDraw_Microsoft_Maui_Controls_BindableObject_System_Object_System_Object_">SkiaControl.NeedDraw(BindableObject, object, object)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NeedRepaint_Microsoft_Maui_Controls_BindableObject_System_Object_System_Object_">SkiaControl.NeedRepaint(BindableObject, object, object)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_InvalidateMeasure">SkiaControl.InvalidateMeasure()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NeedInvalidateViewport_Microsoft_Maui_Controls_BindableObject_System_Object_System_Object_">SkiaControl.NeedInvalidateViewport(BindableObject, object, object)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CreateContentFromTemplate">SkiaControl.CreateContentFromTemplate()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ItemTemplateChanged_Microsoft_Maui_Controls_BindableObject_System_Object_System_Object_">SkiaControl.ItemTemplateChanged(BindableObject, object, object)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetAnimatorsManager">SkiaControl.GetAnimatorsManager()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RegisterAnimator_DrawnUi_Draw_ISkiaAnimator_">SkiaControl.RegisterAnimator(ISkiaAnimator)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UnregisterAnimator_System_Guid_">SkiaControl.UnregisterAnimator(Guid)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UnregisterAllAnimatorsByType_System_Type_">SkiaControl.UnregisterAllAnimatorsByType(Type)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PlayRippleAnimation_Microsoft_Maui_Graphics_Color_System_Double_System_Double_System_Boolean_">SkiaControl.PlayRippleAnimation(Color, double, double, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PlayShimmerAnimation_Microsoft_Maui_Graphics_Color_System_Single_System_Single_System_Int32_System_Boolean_">SkiaControl.PlayShimmerAnimation(Color, float, float, int, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CreateGradient_SkiaSharp_SKRect_DrawnUi_Draw_SkiaGradient_">SkiaControl.CreateGradient(SKRect, SkiaGradient)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LastGradient">SkiaControl.LastGradient</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LastShadow">SkiaControl.LastShadow</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SetupShadow_SkiaSharp_SKPaint_DrawnUi_Draw_SkiaShadow_System_Single_">SkiaControl.SetupShadow(SKPaint, SkiaShadow, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetOrderedSubviews_System_Boolean_">SkiaControl.GetOrderedSubviews(bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetUnorderedSubviews_System_Boolean_">SkiaControl.GetUnorderedSubviews(bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Views">SkiaControl.Views</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DisposeChildren">SkiaControl.DisposeChildren()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnWillDisposeWithChildren">SkiaControl.OnWillDisposeWithChildren()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ClearChildren">SkiaControl.ClearChildren()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnChildRemoved_Microsoft_Maui_Controls_Element_System_Int32_">SkiaControl.OnChildRemoved(Element, int)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnChildAdded_Microsoft_Maui_Controls_Element_">SkiaControl.OnChildAdded(Element)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnChildAdded_DrawnUi_Draw_SkiaControl_">SkiaControl.OnChildAdded(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnChildRemoved_DrawnUi_Draw_SkiaControl_">SkiaControl.OnChildRemoved(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnViewAttached">SkiaControl.OnViewAttached()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnViewDetached">SkiaControl.OnViewDetached()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GestureListenerRegistrationTime">SkiaControl.GestureListenerRegistrationTime</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RegisterGestureListener_DrawnUi_Draw_ISkiaGestureListener_">SkiaControl.RegisterGestureListener(ISkiaGestureListener)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UnregisterGestureListener_DrawnUi_Draw_ISkiaGestureListener_">SkiaControl.UnregisterGestureListener(ISkiaGestureListener)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GestureListeners">SkiaControl.GestureListeners</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnParentChanged_DrawnUi_Draw_IDrawnBase_DrawnUi_Draw_IDrawnBase_">SkiaControl.OnParentChanged(IDrawnBase, IDrawnBase)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ClearParent">SkiaControl.ClearParent()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_StopAnimations">SkiaControl.StopAnimations()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SetParent_DrawnUi_Draw_IDrawnBase_">SkiaControl.SetParent(IDrawnBase)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RegisterGestureListenersTree_DrawnUi_Draw_SkiaControl_">SkiaControl.RegisterGestureListenersTree(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UnregisterGestureListenersTree_DrawnUi_Draw_SkiaControl_">SkiaControl.UnregisterGestureListenersTree(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ItemTemplateProperty">SkiaControl.ItemTemplateProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ItemTemplate">SkiaControl.ItemTemplate</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ItemTemplateTypeProperty">SkiaControl.ItemTemplateTypeProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ItemTemplateType">SkiaControl.ItemTemplateType</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddOrRemoveView_DrawnUi_Draw_SkiaControl_System_Boolean_">SkiaControl.AddOrRemoveView(SkiaControl, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_HasItemTemplate">SkiaControl.HasItemTemplate</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GesturesEffect">SkiaControl.GesturesEffect</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RescaleAspect_System_Single_System_Single_SkiaSharp_SKRect_DrawnUi_Draw_TransformAspect_">SkiaControl.RescaleAspect(float, float, SKRect, TransformAspect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Random">SkiaControl.Random</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LastArrangedInside">SkiaControl.LastArrangedInside</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl__arrangedViewportHeightLimit">SkiaControl._arrangedViewportHeightLimit</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl__arrangedViewportWidthLimit">SkiaControl._arrangedViewportWidthLimit</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl__lastMeasuredForScale">SkiaControl._lastMeasuredForScale</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetRandomColor">SkiaControl.GetRandomColor()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CompareFloats_System_Single_System_Single_System_Single_">SkiaControl.CompareFloats(float, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CompareDoubles_System_Double_System_Double_System_Double_">SkiaControl.CompareDoubles(double, double, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CompareRects_SkiaSharp_SKRect_SkiaSharp_SKRect_System_Single_">SkiaControl.CompareRects(SKRect, SKRect, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CompareRectsSize_SkiaSharp_SKRect_SkiaSharp_SKRect_System_Single_">SkiaControl.CompareRectsSize(SKRect, SKRect, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CompareSize_SkiaSharp_SKSize_SkiaSharp_SKSize_System_Single_">SkiaControl.CompareSize(SKSize, SKSize, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CompareVectors_System_Numerics_Vector2_System_Numerics_Vector2_System_Single_">SkiaControl.CompareVectors(Vector2, Vector2, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AreEqual_System_Double_System_Double_System_Double_">SkiaControl.AreEqual(double, double, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AreEqual_System_Single_System_Single_System_Single_">SkiaControl.AreEqual(float, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AreVectorsEqual_System_Numerics_Vector2_System_Numerics_Vector2_System_Single_">SkiaControl.AreVectorsEqual(Vector2, Vector2, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetDirectionType_System_Numerics_Vector2_DrawnUi_Draw_DirectionType_System_Single_">SkiaControl.GetDirectionType(Vector2, DirectionType, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetDirectionType_System_Numerics_Vector2_System_Numerics_Vector2_System_Single_">SkiaControl.GetDirectionType(Vector2, Vector2, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AreClose_System_Single_System_Single_">SkiaControl.AreClose(float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AreClose_System_Double_System_Double_">SkiaControl.AreClose(double, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsOne_System_Double_">SkiaControl.IsOne(double)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.navigationproperty">VisualElement.NavigationProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.styleproperty">VisualElement.StyleProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.inputtransparentproperty">VisualElement.InputTransparentProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isenabledproperty">VisualElement.IsEnabledProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.xproperty">VisualElement.XProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.yproperty">VisualElement.YProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.anchorxproperty">VisualElement.AnchorXProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.anchoryproperty">VisualElement.AnchorYProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.translationxproperty">VisualElement.TranslationXProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.translationyproperty">VisualElement.TranslationYProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.widthproperty">VisualElement.WidthProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.heightproperty">VisualElement.HeightProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationproperty">VisualElement.RotationProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationxproperty">VisualElement.RotationXProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationyproperty">VisualElement.RotationYProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scaleproperty">VisualElement.ScaleProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scalexproperty">VisualElement.ScaleXProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scaleyproperty">VisualElement.ScaleYProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.clipproperty">VisualElement.ClipProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.visualproperty">VisualElement.VisualProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isvisibleproperty">VisualElement.IsVisibleProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.opacityproperty">VisualElement.OpacityProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.backgroundcolorproperty">VisualElement.BackgroundColorProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.backgroundproperty">VisualElement.BackgroundProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.behaviorsproperty">VisualElement.BehaviorsProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.triggersproperty">VisualElement.TriggersProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.widthrequestproperty">VisualElement.WidthRequestProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.heightrequestproperty">VisualElement.HeightRequestProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.minimumwidthrequestproperty">VisualElement.MinimumWidthRequestProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.minimumheightrequestproperty">VisualElement.MinimumHeightRequestProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.maximumwidthrequestproperty">VisualElement.MaximumWidthRequestProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.maximumheightrequestproperty">VisualElement.MaximumHeightRequestProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isfocusedproperty">VisualElement.IsFocusedProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.flowdirectionproperty">VisualElement.FlowDirectionProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.windowproperty">VisualElement.WindowProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.shadowproperty">VisualElement.ShadowProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.zindexproperty">VisualElement.ZIndexProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.batchbegin">VisualElement.BatchBegin()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.batchcommit">VisualElement.BatchCommit()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.focus">VisualElement.Focus()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measure#microsoft-maui-controls-visualelement-measure(system-double-system-double)">VisualElement.Measure(double, double)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measure#microsoft-maui-controls-visualelement-measure(system-double-system-double-microsoft-maui-controls-measureflags)">VisualElement.Measure(double, double, MeasureFlags)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.unfocus">VisualElement.Unfocus()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onchildrenreordered">VisualElement.OnChildrenReordered()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onmeasure">VisualElement.OnMeasure(double, double)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onsizeallocated">VisualElement.OnSizeAllocated(double, double)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.sizeallocated">VisualElement.SizeAllocated(double, double)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.changevisualstate">VisualElement.ChangeVisualState()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.refreshisenabledproperty">VisualElement.RefreshIsEnabledProperty()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.arrange">VisualElement.Arrange(Rect)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.arrangeoverride">VisualElement.ArrangeOverride(Rect)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.layout">VisualElement.Layout(Rect)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.invalidatemeasureoverride">VisualElement.InvalidateMeasureOverride()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measureoverride">VisualElement.MeasureOverride(double, double)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.mapbackgroundcolor">VisualElement.MapBackgroundColor(IViewHandler, IView)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.mapbackgroundimagesource">VisualElement.MapBackgroundImageSource(IViewHandler, IView)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.visual">VisualElement.Visual</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.flowdirection">VisualElement.FlowDirection</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.window">VisualElement.Window</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.anchorx">VisualElement.AnchorX</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.anchory">VisualElement.AnchorY</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.backgroundcolor">VisualElement.BackgroundColor</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.background">VisualElement.Background</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.behaviors">VisualElement.Behaviors</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.bounds">VisualElement.Bounds</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.heightrequest">VisualElement.HeightRequest</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.inputtransparent">VisualElement.InputTransparent</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isenabled">VisualElement.IsEnabled</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isenabledcore">VisualElement.IsEnabledCore</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isfocused">VisualElement.IsFocused</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isvisible">VisualElement.IsVisible</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.minimumheightrequest">VisualElement.MinimumHeightRequest</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.minimumwidthrequest">VisualElement.MinimumWidthRequest</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.maximumheightrequest">VisualElement.MaximumHeightRequest</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.maximumwidthrequest">VisualElement.MaximumWidthRequest</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.opacity">VisualElement.Opacity</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotation">VisualElement.Rotation</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationx">VisualElement.RotationX</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationy">VisualElement.RotationY</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scalex">VisualElement.ScaleX</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scaley">VisualElement.ScaleY</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.translationx">VisualElement.TranslationX</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.translationy">VisualElement.TranslationY</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.triggers">VisualElement.Triggers</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.widthrequest">VisualElement.WidthRequest</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.clip">VisualElement.Clip</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.resources">VisualElement.Resources</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.frame">VisualElement.Frame</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.handler">VisualElement.Handler</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.shadow">VisualElement.Shadow</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.zindex">VisualElement.ZIndex</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.desiredsize">VisualElement.DesiredSize</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isloaded">VisualElement.IsLoaded</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.childrenreordered">VisualElement.ChildrenReordered</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.focused">VisualElement.Focused</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measureinvalidated">VisualElement.MeasureInvalidated</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.sizechanged">VisualElement.SizeChanged</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.unfocused">VisualElement.Unfocused</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.loaded">VisualElement.Loaded</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.unloaded">VisualElement.Unloaded</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.navigableelement.onparentset">NavigableElement.OnParentSet()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.navigableelement.navigation">NavigableElement.Navigation</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.styleableelement.style">StyleableElement.Style</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.styleableelement.styleclass">StyleableElement.StyleClass</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.styleableelement.class">StyleableElement.class</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.automationidproperty">Element.AutomationIdProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.classidproperty">Element.ClassIdProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.insertlogicalchild">Element.InsertLogicalChild(int, Element)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.addlogicalchild">Element.AddLogicalChild(Element)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removelogicalchild">Element.RemoveLogicalChild(Element)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.clearlogicalchildren">Element.ClearLogicalChildren()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.findbyname">Element.FindByName(string)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removedynamicresource">Element.RemoveDynamicResource(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.setdynamicresource">Element.SetDynamicResource(BindableProperty, string)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanging">Element.OnParentChanging(ParentChangingEventArgs)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanged">Element.OnParentChanged()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onhandlerchanging">Element.OnHandlerChanging(HandlerChangingEventArgs)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onhandlerchanged">Element.OnHandlerChanged()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesisinaccessibletree">Element.MapAutomationPropertiesIsInAccessibleTree(IElementHandler, Element)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesexcludedwithchildren">Element.MapAutomationPropertiesExcludedWithChildren(IElementHandler, Element)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.automationid">Element.AutomationId</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.classid">Element.ClassId</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.effects">Element.Effects</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.id">Element.Id</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.styleid">Element.StyleId</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.childadded">Element.ChildAdded</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.childremoved">Element.ChildRemoved</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.descendantadded">Element.DescendantAdded</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.descendantremoved">Element.DescendantRemoved</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.parentchanging">Element.ParentChanging</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.handlerchanging">Element.HandlerChanging</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.handlerchanged">Element.HandlerChanged</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontextproperty">BindableObject.BindingContextProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindableproperty)">BindableObject.ClearValue(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindablepropertykey)">BindableObject.ClearValue(BindablePropertyKey)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.getvalue">BindableObject.GetValue(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.isset">BindableObject.IsSet(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.removebinding">BindableObject.RemoveBinding(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setbinding">BindableObject.SetBinding(BindableProperty, BindingBase)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.applybindings">BindableObject.ApplyBindings()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onpropertychanging">BindableObject.OnPropertyChanging(string)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.unapplybindings">BindableObject.UnapplyBindings()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindableproperty-system-object)">BindableObject.SetValue(BindableProperty, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindablepropertykey-system-object)">BindableObject.SetValue(BindablePropertyKey, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindableproperty)">BindableObject.CoerceValue(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindablepropertykey)">BindableObject.CoerceValue(BindablePropertyKey)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.dispatcher">BindableObject.Dispatcher</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontext">BindableObject.BindingContext</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.propertychanged">BindableObject.PropertyChanged</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.propertychanging">BindableObject.PropertyChanging</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontextchanged">BindableObject.BindingContextChanged</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </dd></dl>

  <dl class="typelist extensionMethods">
    <dt>Extension Methods</dt>
    <dd>
  <div>
      <a class="xref" href="DrawnUi.Draw.DrawnExtensions.html#DrawnUi_Draw_DrawnExtensions_GetVelocityRatioForChild_DrawnUi_Draw_IDrawnBase_DrawnUi_Draw_ISkiaControl_">DrawnExtensions.GetVelocityRatioForChild(IDrawnBase, ISkiaControl)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.AnimateExtensions.html#DrawnUi_Draw_AnimateExtensions_AnimateWith_DrawnUi_Draw_SkiaControl_System_Func_DrawnUi_Draw_SkiaControl_System_Threading_Tasks_Task____">AnimateExtensions.AnimateWith(SkiaControl, params Func&lt;SkiaControl, Task&gt;[])</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.AnimateExtensions.html#DrawnUi_Draw_AnimateExtensions_FadeIn_DrawnUi_Draw_SkiaControl_System_Single_Microsoft_Maui_Easing_System_Threading_CancellationTokenSource_">AnimateExtensions.FadeIn(SkiaControl, float, Easing, CancellationTokenSource)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.AnimateExtensions.html#DrawnUi_Draw_AnimateExtensions_FadeOut_DrawnUi_Draw_SkiaControl_System_Single_Microsoft_Maui_Easing_System_Threading_CancellationTokenSource_">AnimateExtensions.FadeOut(SkiaControl, float, Easing, CancellationTokenSource)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.AnimateExtensions.html#DrawnUi_Draw_AnimateExtensions_Translate_DrawnUi_Draw_SkiaControl_System_Numerics_Vector2_System_Single_Microsoft_Maui_Easing_System_Threading_CancellationTokenSource_">AnimateExtensions.Translate(SkiaControl, Vector2, float, Easing, CancellationTokenSource)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.DrawnExtensions.html#DrawnUi_Draw_DrawnExtensions_AnimateRangeAsync_DrawnUi_Draw_SkiaControl_System_Action_System_Double__System_Double_System_Double_System_UInt32_Microsoft_Maui_Easing_System_Threading_CancellationTokenSource_">DrawnExtensions.AnimateRangeAsync(SkiaControl, Action&lt;double&gt;, double, double, uint, Easing, CancellationTokenSource)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParentByType__1_DrawnUi_Draw_SkiaControl_">StaticResourcesExtensions.FindParentByType&lt;T&gt;(SkiaControl)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithColumnDefinitions_DrawnUi_Draw_SkiaLayout_System_String_">FluentExtensions.WithColumnDefinitions(SkiaLayout, string)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithRowDefinitions_DrawnUi_Draw_SkiaLayout_System_String_">FluentExtensions.WithRowDefinitions(SkiaLayout, string)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_Adapt__1___0_System_Action___0__">FluentExtensions.Adapt&lt;T&gt;(T, Action&lt;T&gt;)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_AssignNative__1___0___0__">FluentExtensions.AssignNative&lt;T&gt;(T, out T)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_AssignParent__1___0_DrawnUi_Draw_SkiaControl_">FluentExtensions.AssignParent&lt;T&gt;(T, SkiaControl)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_Assign__1___0___0__">FluentExtensions.Assign&lt;T&gt;(T, out T)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_BindProperty__1___0_Microsoft_Maui_Controls_BindableProperty_System_ComponentModel_INotifyPropertyChanged_System_String_Microsoft_Maui_Controls_BindingMode_">FluentExtensions.BindProperty&lt;T&gt;(T, BindableProperty, INotifyPropertyChanged, string, BindingMode)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_BindProperty__1___0_Microsoft_Maui_Controls_BindableProperty_System_String_Microsoft_Maui_Controls_BindingMode_">FluentExtensions.BindProperty&lt;T&gt;(T, BindableProperty, string, BindingMode)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_BindProperty__2___0_Microsoft_Maui_Controls_BindableProperty_System_String_Microsoft_Maui_Controls_IValueConverter_System_Object_Microsoft_Maui_Controls_BindingMode_">FluentExtensions.BindProperty&lt;T, TProperty&gt;(T, BindableProperty, string, IValueConverter, object, BindingMode)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_CenterX__1___0_">FluentExtensions.CenterX&lt;T&gt;(T)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_CenterY__1___0_">FluentExtensions.CenterY&lt;T&gt;(T)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_Center__1___0_">FluentExtensions.Center&lt;T&gt;(T)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_EndX__1___0_">FluentExtensions.EndX&lt;T&gt;(T)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_EndY__1___0_">FluentExtensions.EndY&lt;T&gt;(T)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_FillX__1___0_">FluentExtensions.FillX&lt;T&gt;(T)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_FillY__1___0_">FluentExtensions.FillY&lt;T&gt;(T)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_Fill__1___0_">FluentExtensions.Fill&lt;T&gt;(T)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_Height__1___0_System_Double_">FluentExtensions.Height&lt;T&gt;(T, double)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_Initialize__1___0_System_Action___0__">FluentExtensions.Initialize&lt;T&gt;(T, Action&lt;T&gt;)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_ObserveBindingContext__2___0_System_Action___0___1_System_String__System_Boolean_">FluentExtensions.ObserveBindingContext&lt;T, TSource&gt;(T, Action&lt;T, TSource, string&gt;, bool)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_ObserveDeepNestedProperty__5___0_System_Func___1___2__System_String_System_Func___2___3__System_String_System_Func___3___4__System_String_System_Action___0___4____4_System_Boolean_">FluentExtensions.ObserveDeepNestedProperty&lt;T, TSource, TIntermediate1, TIntermediate2, TProperty&gt;(T, Func&lt;TSource, TIntermediate1&gt;, string, Func&lt;TIntermediate1, TIntermediate2&gt;, string, Func&lt;TIntermediate2, TProperty&gt;, string, Action&lt;T, TProperty&gt;, TProperty, bool)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_ObserveDeep__4___0_System_Func___1___2__System_String_System_Func___2___3__System_String_System_Action___0___3____3_System_Boolean_">FluentExtensions.ObserveDeep&lt;T, TSource, TIntermediate, TProperty&gt;(T, Func&lt;TSource, TIntermediate&gt;, string, Func&lt;TIntermediate, TProperty&gt;, string, Action&lt;T, TProperty&gt;, TProperty, bool)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_ObserveSelf__1___0_System_Action___0_System_String__">FluentExtensions.ObserveSelf&lt;T&gt;(T, Action&lt;T, string&gt;)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_ObserveTargetBindingContext__3___0___1_System_Action___0___1___2_System_String__System_Boolean_">FluentExtensions.ObserveTargetBindingContext&lt;T, TTarget, TSource&gt;(T, TTarget, Action&lt;T, TTarget, TSource, string&gt;, bool)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_ObserveTargetProperty__4___0_System_Linq_Expressions_Expression_System_Func___1___2___System_Linq_Expressions_Expression_System_Func___2___3___System_Action___0___3____3_System_Boolean_">FluentExtensions.ObserveTargetProperty&lt;T, TSource, TIntermediate, TProperty&gt;(T, Expression&lt;Func&lt;TSource, TIntermediate&gt;&gt;, Expression&lt;Func&lt;TIntermediate, TProperty&gt;&gt;, Action&lt;T, TProperty&gt;, TProperty, bool)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_Observe__2___0_System_Func___1__System_Action___0_System_String__System_String___">FluentExtensions.Observe&lt;T, TSource&gt;(T, Func&lt;TSource&gt;, Action&lt;T, string&gt;, string[])</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_Observe__2___0___1_System_Action___0_System_String__System_String___">FluentExtensions.Observe&lt;T, TSource&gt;(T, TSource, Action&lt;T, string&gt;, string[])</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_OnBindingContextSet__1___0_System_Action___0_System_Object__System_String___">FluentExtensions.OnBindingContextSet&lt;T&gt;(T, Action&lt;T, object&gt;, string[])</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_OnLongPressing__1___0_System_Action___0__">FluentExtensions.OnLongPressing&lt;T&gt;(T, Action&lt;T&gt;)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_OnPaint__1___0_System_Action___0_DrawnUi_Draw_DrawingContext__">FluentExtensions.OnPaint&lt;T&gt;(T, Action&lt;T, DrawingContext&gt;)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_OnTapped__1___0_System_Action___0__">FluentExtensions.OnTapped&lt;T&gt;(T, Action&lt;T&gt;)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_SetGrid__1___0_System_Int32_System_Int32_">FluentExtensions.SetGrid&lt;T&gt;(T, int, int)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_SetGrid__1___0_System_Int32_System_Int32_System_Int32_System_Int32_">FluentExtensions.SetGrid&lt;T&gt;(T, int, int, int, int)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_SetMargin__1___0_System_Double_">FluentExtensions.SetMargin&lt;T&gt;(T, double)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_SetMargin__1___0_System_Double_System_Double_">FluentExtensions.SetMargin&lt;T&gt;(T, double, double)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_SetMargin__1___0_System_Double_System_Double_System_Double_System_Double_">FluentExtensions.SetMargin&lt;T&gt;(T, double, double, double, double)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_SetWidth__1___0_System_Double_">FluentExtensions.SetWidth&lt;T&gt;(T, double)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_StartX__1___0_">FluentExtensions.StartX&lt;T&gt;(T)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_StartY__1___0_">FluentExtensions.StartY&lt;T&gt;(T)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithBackgroundColor__1___0_Microsoft_Maui_Graphics_Color_">FluentExtensions.WithBackgroundColor&lt;T&gt;(T, Color)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithCache__1___0_DrawnUi_Draw_SkiaCacheType_">FluentExtensions.WithCache&lt;T&gt;(T, SkiaCacheType)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithChildren__1___0_DrawnUi_Draw_SkiaControl___">FluentExtensions.WithChildren&lt;T&gt;(T, params SkiaControl[])</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithColumnSpan__1___0_System_Int32_">FluentExtensions.WithColumnSpan&lt;T&gt;(T, int)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithColumn__1___0_System_Int32_">FluentExtensions.WithColumn&lt;T&gt;(T, int)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithGestures__1___0_System_Func___0_DrawnUi_Draw_SkiaGesturesParameters_DrawnUi_Draw_GestureEventProcessingInfo_DrawnUi_Draw_ISkiaGestureListener__">FluentExtensions.WithGestures&lt;T&gt;(T, Func&lt;T, SkiaGesturesParameters, GestureEventProcessingInfo, ISkiaGestureListener&gt;)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithHeightRequest__1___0_System_Double_">FluentExtensions.WithHeightRequest&lt;T&gt;(T, double)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithHorizontalOptions__1___0_Microsoft_Maui_Controls_LayoutOptions_">FluentExtensions.WithHorizontalOptions&lt;T&gt;(T, LayoutOptions)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithMargin__1___0_Microsoft_Maui_Thickness_">FluentExtensions.WithMargin&lt;T&gt;(T, Thickness)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithPadding__1___0_System_Double_">FluentExtensions.WithPadding&lt;T&gt;(T, double)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithPadding__1___0_System_Double_System_Double_">FluentExtensions.WithPadding&lt;T&gt;(T, double, double)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithPadding__1___0_System_Double_System_Double_System_Double_System_Double_">FluentExtensions.WithPadding&lt;T&gt;(T, double, double, double, double)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithParent__1___0_DrawnUi_Draw_IDrawnBase_">FluentExtensions.WithParent&lt;T&gt;(T, IDrawnBase)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithRowSpan__1___0_System_Int32_">FluentExtensions.WithRowSpan&lt;T&gt;(T, int)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithRow__1___0_System_Int32_">FluentExtensions.WithRow&lt;T&gt;(T, int)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithTag__1___0_System_String_">FluentExtensions.WithTag&lt;T&gt;(T, string)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithVerticalOptions__1___0_Microsoft_Maui_Controls_LayoutOptions_">FluentExtensions.WithVerticalOptions&lt;T&gt;(T, LayoutOptions)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithWidthRequest__1___0_System_Double_">FluentExtensions.WithWidthRequest&lt;T&gt;(T, double)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_">StaticResourcesExtensions.FindParent&lt;T&gt;(Element)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_">InternalExtensions.FindMauiContext(Element, bool)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_">InternalExtensions.GetParentsPath(Element)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_GetAllWithMyselfParents_Microsoft_Maui_Controls_VisualElement_">StaticResourcesExtensions.GetAllWithMyselfParents(VisualElement)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_DisposeControlAndChildren_Microsoft_Maui_IView_">InternalExtensions.DisposeControlAndChildren(IView)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_">InternalExtensions.FromPlatform(object)</a>
  </div>
  </dd></dl>





  <h2 class="section" id="constructors">Constructors
</h2>


  <a id="DrawnUi_Draw_SkiaStack__ctor_" data-uid="DrawnUi.Draw.SkiaStack.#ctor*"></a>

  <h3 id="DrawnUi_Draw_SkiaStack__ctor" data-uid="DrawnUi.Draw.SkiaStack.#ctor">
  SkiaStack()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Fluent/SkiaStack.cs/#L8"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SkiaStack()</code></pre>
  </div>














</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Features/Fluent/SkiaStack.cs/#L6" class="edit-link">Edit this page</a>
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
